<?php
// Rich Snippets Settings Overlay
// This overlay allows users to manage rich snippet settings for a project

// Get project ID from the URL
$projectId = $data['id'] ?? 0;

// Initialize RichSnippetManager
$richSnippetManager = new RichSnippetManager();

// Get rich snippets for this project
$richSnippets = $richSnippetManager->getRichSnippetsByProjectId($projectId);

// Count statistics
$totalSnippets = count($richSnippets);
$generatedSnippets = 0;
$publishedSnippets = 0;
$globalSnippet = null;

foreach ($richSnippets as $snippet) {
    if ($snippet['is_generated']) {
        $generatedSnippets++;
    }
    if ($snippet['is_published']) {
        $publishedSnippets++;
    }
    if ($snippet['is_global_snippet']) {
        $globalSnippet = $snippet;
    }
}
?>

<div class="modal modal-blur fade show" id="page-rich-snippet-modal" tabindex="-1" role="dialog" aria-modal="true" style="display: block;">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
<div class="modal-content">
    <div class="modal-header">
        <h5 class="modal-title">Rich Snippets Einstellungen</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
    
    <div class="modal-body">
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Übersicht</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="stat-box">
                                    <span class="stat-label">Gesamt</span>
                                    <span class="stat-value"><?= $totalSnippets ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-box">
                                    <span class="stat-label">Generiert</span>
                                    <span class="stat-value"><?= $generatedSnippets ?></span>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="stat-box">
                                    <span class="stat-label">Veröffentlicht</span>
                                    <span class="stat-value"><?= $publishedSnippets ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">Aktionen</h5>
                        <div class="d-flex gap-2">

                            <form action="" method="post" class="d-inline">
                                <input type="hidden" name="class" value="RichSnippetManager">
                                <input type="hidden" name="method" value="scanPagesByProjectId">
                                <input type="hidden" name="projectId" value="<?php echo $projectId; ?>">
                                <input type="hidden" name="action" value="execute">
                                <input type="hidden" name="show_loading" value="1">
                                <button type="submit" class="btn btn-primary">
                                    Seiten scannen
                                </button>
                            </form>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <form id="rich-snippet-context-form" action="" method="post" style="display: block">
            <input type="hidden" name="table" value="projects">
            <input type="hidden" name="id" value="<?php echo $data['id']; ?>">
            <input type="hidden" name="field" value="rich_snippet_options_data">
            <input type="hidden" name="action" value="update_merge_data">

            <div class="mb-3">
                <label class="form-label">Rich Snippet Kontext</label>
                <textarea class="form-control" name="context" rows="10"><?php if(isset($data['rich_snippet_options_data']['context'])) {echo $data['rich_snippet_options_data']['context']; } ?></textarea>
                <div class="form-text text-muted">
                    Logo Url:<br>
                    Image Url:<br>
                    Öffnungszeiten:<br>
                    priceRange: "€€"<br>
                    Social Urls: LinkedIn, Instagram, Facebook<br>
                    GeoCoordinates, latitude, longitude<br>
                    "@type": ["LocalBusiness", "MarketingAgency"]<br>
                </div>
            </div>
        </form>

    </div>
    
    <div class="modal-footer">
        <button type="submit" form="rich-snippet-context-form" class="btn btn-primary">
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-device-floppy">
                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                <path d="M6 4h10l4 4v10a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2" />
                <path d="M12 14m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0" />
                <path d="M14 4l0 4l-6 0l0 -4" />
            </svg>
            Speichern
        </button>
    </div>
</div>
</div>
</div>