<?php

/**
 * Website Manager Class
 *
 * A class that uses WebsiteCrawler/ScrapingAntCrawler and AiManager to extract information from websites
 * Provides methods to find imprint links, extract company details, contact information, etc.
 */
class WebsiteManager
{
    private AiManager $aiManager;

    /** @var WebsiteCrawler  */
    private $websiteCrawler = null;
    private array $websiteData = [];
    private array $companyData = [];
    private bool $useScrapingAnt = true;
    private ?string $scrapingAntApiToken = null;

    /**
     * Constructor
     *
     * @param AiManager $aiManager The AI manager instance to use for analysis
     * @param bool $useScrapingAnt Whether to use ScrapingAnt for crawling (default: false)
     */
    public function __construct(AiManager $aiManager, bool $useScrapingAnt = true)
    {
        $this->aiManager = $aiManager;
        $this->useScrapingAnt = $useScrapingAnt;

        if ($this->useScrapingAnt) {
            // Load ScrapingAnt API token from config
            require_once __DIR__ . '/scraping_ant_crawler.php';
            $this->scrapingAntApiToken = ConfigManager::get('scraping_ant.api_token');

            if (empty($this->scrapingAntApiToken)) {
                throw new \InvalidArgumentException("ScrapingAnt API token not found in configuration.");
            }
        }
    }

    /**
     * Analyze a website to extract information
     *
     * @param string $url The URL of the website to analyze
     * @param array $crawlerOptions Additional options for the crawler (ScrapingAnt API options)
     * @return bool True if the analysis was successful, false otherwise
     */
    public function analyzeWebsite(string $url, array $crawlerOptions = []): bool
    {
        // Reset data
        $this->websiteData = [];
        $this->companyData = [];

        // Create a new crawler instance based on configuration
        if ($this->useScrapingAnt && $this->scrapingAntApiToken) {
            // Use ScrapingAntCrawler
            $this->websiteCrawler = new ScrapingAntCrawler($this->scrapingAntApiToken, $url);

            // Set API options if provided
            if (!empty($crawlerOptions)) {
                $this->websiteCrawler->setApiOptions($crawlerOptions);
            }
        } else {
            // Use standard WebsiteCrawler
            $this->websiteCrawler = new WebsiteCrawler($url);
        }

        // Fetch the website content
        $this->websiteCrawler->fetch();

        // Check if the website was successfully fetched
        if ($this->websiteCrawler->getStatusCode() != 200) {
            return false;
        }

        //$this->extractImportantUrls();
        //$this->extractCompanyInfo();

        return true;
    }

    /**
     * Find important URLs on the website (imprint, jobs, blog)
     *
     * @return bool True if the imprint URL was found, false otherwise
     */
    private function extractImportantUrls(): bool
    {

        if( $this->websiteData )
            return true;

        // Get internal links from the website
        $internalLinks = $this->websiteCrawler->getInternalLinks();
        $internalLinksString = implode(', ', $internalLinks);

        // Use AI to identify important URLs
        $systemPrompt = '{"imprint_url":"", "jobs_url":"", "blog_url":""}
        imprint_url = Url des Impressum, jobs_url = Url der Job oder Karriere Seite, blog_url = Blog News oder Aktuelles Url.
        JSON befüllen und OHNE Kommentare oder Formatierung ausgeben. Wenn Link nicht gefunden wird, Feld leer ausgeben.';

        try {
            $urlAnalysisResult = $this->aiManager->generateContent($internalLinksString, $systemPrompt);

//print_r($urlAnalysisResult);
//exit;

            $urlData = json_decode($urlAnalysisResult, true);

            if (!$urlData || !isset($urlData['imprint_url']) || empty($urlData['imprint_url'])) {
                return false;
            }

            $this->websiteData = $urlData;
            return true;
        } catch (Exception $e) {

            echo 'ERROR: '. $e->getMessage();

            return false;
        }
    }

    /**
     * Extract company information from the imprint page
     *
     * @param array $crawlerOptions Additional options for the crawler (ScrapingAnt API options)
     * @return bool True if company information was successfully extracted, false otherwise
     */
    public function extractCompanyInfo(array $crawlerOptions = []): bool
    {
        if( $this->companyData )
            return true;

        $this->extractImportantUrls();

        // Create a new crawler for the imprint page based on configuration
        if ($this->useScrapingAnt && $this->scrapingAntApiToken) {
            // Use ScrapingAntCrawler
            $imprintCrawler = new ScrapingAntCrawler(
                $this->scrapingAntApiToken,
                $this->websiteData['imprint_url'],
                $this->websiteCrawler->getUrl()
            );

            // Set API options if provided
            if (!empty($crawlerOptions)) {
                $imprintCrawler->setApiOptions($crawlerOptions);
            }
        } else {
            // Use standard WebsiteCrawler
            $imprintCrawler = new WebsiteCrawler($this->websiteData['imprint_url'], $this->websiteCrawler->getUrl());
        }

        // Fetch the imprint page content
        $imprintCrawler->fetch();

        // Check if the imprint page was successfully fetched
        if ($imprintCrawler->getStatusCode() != 200) {
            return false;
        }

        // Get the full text from the imprint page
        $imprintFullText = $imprintCrawler->getFullText();
        if (empty($imprintFullText)) {
            return false;
        }

        // Use AI to extract company information
        $systemPrompt = '
        {"name":"", "contact_name":"", "phone":"", "email":"", "company":"", "street":"", "zip":"", "city":"", "about":""}
        JSON auf deutsch befüllen und OHNE Kommentare oder Formatierung ausgeben. Wenn Werte nicht gefunden wurden, leeren Wert "" ausgeben.
        name = Name umgangssprachlich ohne Rechtsform,
        contact_name = Name Ansprechpartner,
        phone = Telefonnummer,
        email = Hauptemail,
        company = Firma mit Rechtsform,
        street = Straße und Hausnummer
        zip = Postleitzahl,
        city = Stadt,
        about = Kurze Beschreibung.';

        try {
            //$this->aiManager->setModel('gemini-2.0-flash');
            $companyAnalysisResult = $this->aiManager->generateContent($imprintFullText, $systemPrompt);
            $companyData = json_decode($companyAnalysisResult, true);

            $this->companyData = $companyData;
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Get the extracted website data (URLs)
     *
     * @return array The website data
     */
    public function getWebsiteData(): array
    {
        $this->extractImportantUrls();

        return $this->websiteData;
    }

    /**
     * Get the extracted company data
     *
     * @return array The company data
     */
    public function getCompanyData(): array
    {
        $this->extractCompanyInfo();

        return $this->companyData;
    }

    /**
     * Get all extracted data (website and company)
     *
     * @return array All extracted data
     */
    public function getAllData(): array
    {
        $this->extractImportantUrls();
        $this->extractCompanyInfo();

        return [
            'website' => $this->websiteData,
            'company' => $this->companyData
        ];
    }

    /**
     * Get the WebsiteCrawler instance
     *
     * @return WebsiteCrawler|null The WebsiteCrawler instance or null if not initialized
     */
    public function getWebsiteCrawler(): ?WebsiteCrawler
    {
        return $this->websiteCrawler;
    }

    /**
     * Extract job information from the jobs page if available
     *
     * @param array $crawlerOptions Additional options for the crawler (ScrapingAnt API options)
     * @return string|null The job information or null if not available
     */
    public function extractJobInfo(array $crawlerOptions = []): ?string
    {
        $this->extractImportantUrls();

        // Check if jobs URL is available
        if (empty($this->websiteData['jobs_url'])) {
            return null;
        }

        // Create a new crawler for the jobs page based on configuration
        if ($this->useScrapingAnt && $this->scrapingAntApiToken) {
            // Use ScrapingAntCrawler
            $jobsCrawler = new ScrapingAntCrawler(
                $this->scrapingAntApiToken,
                $this->websiteData['jobs_url'],
                $this->websiteCrawler->getUrl()
            );

            // Set API options if provided
            if (!empty($crawlerOptions)) {
                $jobsCrawler->setApiOptions($crawlerOptions);
            }
        } else {
            // Use standard WebsiteCrawler
            $jobsCrawler = new WebsiteCrawler($this->websiteData['jobs_url'], $this->websiteCrawler->getUrl());
        }

        // Fetch the jobs page content
        $jobsCrawler->fetch();

        // Check if the jobs page was successfully fetched
        if ($jobsCrawler->getStatusCode() != 200) {
            return null;
        }

        // Get the full text from the jobs page
        $jobsFullText = $jobsCrawler->getFullText();
        if (empty($jobsFullText)) {
            return null;
        }

        // Use AI to extract job information
        $systemPrompt = 'Welche Jobs werden gesucht? Als kurze Zusammenfassung ausgeben';

        try {
            return $this->aiManager->generateContent($jobsFullText, $systemPrompt);
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Extract blog information if available
     *
     * @param array $crawlerOptions Additional options for the crawler (ScrapingAnt API options)
     * @return string|null The blog information or null if not available
     */
    public function extractBlogInfo(array $crawlerOptions = []): ?string
    {

        $this->extractImportantUrls();

        // Check if blog URL is available
        if (empty($this->websiteData['blog_url'])) {
            return null;
        }

        // Create a new crawler for the blog page based on configuration
        if ($this->useScrapingAnt && $this->scrapingAntApiToken) {
            // Use ScrapingAntCrawler
            $blogCrawler = new ScrapingAntCrawler(
                $this->scrapingAntApiToken,
                $this->websiteData['blog_url'],
                $this->websiteCrawler->getUrl()
            );

            // Set API options if provided
            if (!empty($crawlerOptions)) {
                $blogCrawler->setApiOptions($crawlerOptions);
            }
        } else {
            // Use standard WebsiteCrawler
            $blogCrawler = new WebsiteCrawler($this->websiteData['blog_url'], $this->websiteCrawler->getUrl());
        }

        // Fetch the blog page content
        $blogCrawler->fetch();

        // Check if the blog page was successfully fetched
        if ($blogCrawler->getStatusCode() != 200) {
            return null;
        }

        // Get the full text from the blog page
        $blogFullText = $blogCrawler->getFullText();
        if (empty($blogFullText)) {
            return null;
        }

        // Use AI to extract blog information
        $systemPrompt = 'Fasse die wichtigsten Themen und Inhalte des Blogs kurz zusammen.';

        try {
            return $this->aiManager->generateContent($blogFullText, $systemPrompt);
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Get SEO information about the website
     *
     * @return array The SEO information
     */
    public function getSeoInfo(): array
    {
        if (!$this->websiteCrawler) {
            return [];
        }

        return $this->websiteCrawler->getSeoSummary();
    }

    /**
     * Get SEO information as text
     *
     * @return string The SEO information as text
     */
    public function getSeoInfoAsText(): string
    {
        if (!$this->websiteCrawler) {
            return '';
        }

        return $this->websiteCrawler->getSeoSummaryAsText();
    }

    /**
     * Check if a string is a valid website URL
     *
     * @param string $input The string to check
     * @return bool True if the string is a valid website URL, false otherwise
     */
    public static function isValidWebsiteUrl(string $input): bool
    {
        // Check if the input matches a basic URL pattern
        return (bool) preg_match('/^(https?:\/\/)?([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}(\/.*)?$/', $input);
    }

    /**
     * Ensure a URL has a scheme (http:// or https://)
     *
     * @param string $url The URL to check and fix
     * @return string The URL with a scheme
     */
    public static function ensureUrlHasScheme(string $url): string
    {
        if (!preg_match('/^https?:\/\//i', $url)) {
            return 'https://' . $url;
        }
        return $url;
    }

    /**
     * Enable or disable the use of ScrapingAnt for crawling
     *
     * @param bool $useScrapingAnt Whether to use ScrapingAnt for crawling
     * @return $this For method chaining
     */
    public function setUseScrapingAnt(bool $useScrapingAnt): self
    {
        $this->useScrapingAnt = $useScrapingAnt;

        if ($this->useScrapingAnt && empty($this->scrapingAntApiToken)) {
            // Load ScrapingAnt API token from config if not already loaded
            require_once __DIR__ . '/scraping_ant_crawler.php';
            $this->scrapingAntApiToken = ConfigManager::get('scraping_ant.api_token');

            if (empty($this->scrapingAntApiToken)) {
                throw new \InvalidArgumentException("ScrapingAnt API token not found in configuration.");
            }
        }

        return $this;
    }

    /**
     * Check if ScrapingAnt is being used for crawling
     *
     * @return bool True if ScrapingAnt is being used, false otherwise
     */
    public function isUsingScrapingAnt(): bool
    {
        return $this->useScrapingAnt && !empty($this->scrapingAntApiToken);
    }

    /**
     * Get the full text content from the current website
     *
     * @return string The full text content or empty string if not available
     */
    public function getFullText(): string
    {
        if (!$this->websiteCrawler) {
            return '';
        }

        return $this->websiteCrawler->getFullText();
    }
}
