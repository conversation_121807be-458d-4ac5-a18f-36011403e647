<?php

class BaseManager {

    /** @var DbManager  */
    private $dbManager;

    /**
     * <PERSON><PERSON><PERSON><PERSON>, der den DbManager injiziert.
     * @param mixed $dbManager Instanz des Datenbank-Managers.
     */
    public function __construct($dbManager) {
        $this->dbManager = $dbManager;
    }

    public function getOverlayContent(array $postData, array $data = []) {
        // Overlay-Template aus POST-Daten auslesen
        $templateName = isset($postData['overlay_template']) ? $postData['overlay_template'] : '';

        // Wenn ein "/" im Template-Namen enthalten ist, den kompletten Pfad verwenden
        if (strpos($templateName, '/') !== false) {
            $templateFile = APP_PATH . $templateName . '.php';
        } else {
            // Sicherheitsmaßnahme: Nur den Dateinamen verwenden
            $templateName = basename($templateName);
            $templateFile = APP_PATH . 'overlays/' . $templateName . '.php';
        }

        $content = '';
        if (file_exists($templateFile)) {
            extract($data);
            ob_start();
            $dbManager = $this->dbManager;
            include $templateFile;
            $content = ob_get_clean();
        }
        return $content;
    }


    /**
     * Verarbeitet die eingehende POST-Anfrage basierend auf dem 'action'-Parameter.
     *
     * @param array $postData Das $_POST Array oder ein äquivalentes Array.
     * @return array Ein Response-Array, das normalerweise 'success' (0 oder 1)
     *               und optional 'error', 'overlay_content', 'new_state', 'last_id' enthält.
     */
    public function handleRequest(array $postData): array {
        $action = $postData['action'] ?? null;
        $response = ['success' => 0]; // Standardmäßig erfolglos

        switch ($action) {
            case 'overlay':
                $response = $this->handleOverlay($postData);
                break;
            case 'add':
                $response = $this->handleAdd($postData);
                break;
            case 'update_data':
                $response = $this->handleUpdateData($postData);
                break;
            case 'update_merge_data':
                $response = $this->handleUpdateMergeData($postData);
                break;
            case 'update_fields':
                $response = $this->handleUpdateFields($postData);
                break;
            case 'delete':
                $response = $this->handleDelete($postData);
                break;
            case 'toggle':
                $response = $this->handleToggle($postData);
                break;
            case 'execute':
                $response = $this->handleExecute($postData);
                break;
            default:
                $response['error'] = 'Invalid action provided.';
                break;
        }

        return $response;
    }

    /**
     * Holt den HTML-Inhalt für ein Overlay/Modal.
     *
     * @param array $postData Daten aus der POST-Anfrage. Erwartet ['id', 'table', 'template'(optional)].
     * @return array Response-Array mit 'overlay_content' oder 'error'.
     */
    private function handleOverlay(array $postData): array {
        $response = ['success' => 0];
        $data = [];

        // Daten nur laden, wenn ID und Tabelle vorhanden sind
        if (isset($postData['id']) && !empty($postData['id']) && isset($postData['table']) && !empty($postData['table'])) {
            try {
                // Fehler abfangen, falls findById fehlschlägt oder null zurückgibt
                $fetchedData = $this->dbManager->findById($postData['id'], $postData['table']);
                if ($fetchedData) {
                    $data = $fetchedData;
                } else {
                    // Optional: Fehler loggen oder spezifischer behandeln
                    // $response['error'] = "Datensatz mit ID {$postData['id']} in Tabelle {$postData['table']} nicht gefunden.";
                    // return $response; // Frühzeitig beenden, wenn Daten benötigt werden, aber nicht gefunden wurden
                }
            } catch (Exception $e) {
                // Log error $e->getMessage();
                $response['error'] = 'Error fetching data for overlay.';
                return $response;
            }
        }

        // Holt den Template-Inhalt (diese Methode muss implementiert werden)
        $overlayContent = $this->getOverlayContent($postData, $data);

        if ($overlayContent !== null) {
            $response['overlay_content'] = $overlayContent;
            $response['success'] = 1;
        } else {
            $response['error'] = 'Overlay template not found or error generating content.';
            // Behalte success = 0
        }
        return $response;
    }

    /**
     * Fügt eine neue Zeile in die Datenbank ein.
     *
     * @param array $postData Daten aus der POST-Anfrage. Erwartet ['table', 'field'(optional), ...daten...].
     * @return array Response-Array mit 'success' und ggf. 'last_id' oder 'error'.
     */
    private function handleAdd(array $postData): array {
        $response = ['success' => 0];

        if (!isset($postData['table']) || empty($postData['table'])) {
            $response['error'] = 'Table parameter is missing for add action.';
            return $response;
        }

        $table = $postData['table'];
        $lastId = null;

        try {
            if (isset($postData['fields']) && !empty($postData['fields'])) {
                // Spezifische Felder aus $postData extrahieren
                $fields = explode(',', $postData['fields']);
                $data = [];
                foreach ($fields as $field) {
                    $field = trim($field);
                    // Sicherstellen, dass nur die angegebenen Felder verwendet werden
                    if (array_key_exists($field, $postData)) {
                        $data[$field] = $postData[$field];
                    }
                }
                if (empty($data)) {
                    $response['error'] = 'No valid fields provided for insertion.';
                    return $response;
                }
                $lastId = $this->dbManager->insertNewRowByData($table, $data);
            } else {
                // Einfaches Einfügen ohne spezifische Daten (oder mit Standarddaten im DbManager)
                $lastId = $this->dbManager->insertNewRow($table);
            }

            if ($lastId !== null && $lastId !== false) { // Prüfe auf gültige ID (nicht null oder false)
                $response['success'] = 1;
                $response['last_id'] = $lastId; // Optional die ID zurückgeben
            } else {
                $response['error'] = 'Failed to insert new row.';
            }
        } catch (Exception $e) {
            // Log error $e->getMessage();
            $response['error'] = 'Database error during add action: ' . $e->getMessage();
        }

        return $response;
    }

    /**
     * Aktualisiert Daten in einer vorhandenen Zeile.
     *
     * @param array $postData Daten aus der POST-Anfrage. Erwartet ['table', 'id', 'field'(optional), ...daten...].
     * @return array Response-Array mit 'success' oder 'error'.
     */
    private function handleUpdateData(array $postData): array {
        $response = ['success' => 0];

        // Grundlegende Parameterprüfung
        if (
            !isset($postData['table']) || empty($postData['table']) ||
            !isset($postData['id']) || empty($postData['id'])
            // 'field' ist hier optional, da updateData es ggf. nicht braucht, wenn ganze Zeile aktualisiert wird
            // !isset($postData['field']) || empty($postData['field'])
        ) {
            $response['error'] = 'Missing required parameters (table, id) for updateData action.';
            return $response;
        }

        // Daten extrahieren, reservierte Schlüssel ausschließen
        $reservedKeys = ['action', 'table', 'id', 'field', 'is_reload'];
        $data = [];
        foreach ($postData as $key => $value) {
            if (!in_array($key, $reservedKeys)) {
                $data[$key] = $value; // Hier keine weitere Filterung nach 'field', updateData kümmert sich darum
            }
        }

        //if (empty($data)) {
        //    $response['error'] = 'No data provided to save.';
        //    return $response;
        //}


        try {
            // Annahme: updateData kann entweder spezifische Felder oder alle übergebenen $data aktualisieren
            // Der Parameter 'field' aus $postData wird hier nicht direkt an updateData übergeben,
            // da die Logik alle nicht-reservierten Keys als Daten nimmt.
            // Passe dies an, falls dein updateData explizit $postData['field'] erwartet.
            $success = $this->dbManager->updateData($postData['id'], $postData['table'], $postData['field'] , $data); // $postData['field'] hier entfernt, $data enthält alles

            if ($success) {
                $response['success'] = 1;
            } else {
                $response['error'] = 'Failed to update data. Record might not exist or data unchanged.';
            }
        } catch (Exception $e) {
            // Log error $e->getMessage();
            $response['error'] = 'Database error during updateData action: ' . $e->getMessage();
        }

        return $response;
    }

    /**
     * Aktualisiert Daten in einer vorhandenen Zeile.
     *
     * @param array $postData Daten aus der POST-Anfrage. Erwartet ['table', 'id', 'field'(optional), ...daten...].
     * @return array Response-Array mit 'success' oder 'error'.
     */
    private function handleUpdateMergeData(array $postData): array {
        $response = ['success' => 0];

        // Grundlegende Parameterprüfung
        if (
            !isset($postData['table']) || empty($postData['table']) ||
            !isset($postData['id']) || empty($postData['id'])
            // 'field' ist hier optional, da updateData es ggf. nicht braucht, wenn ganze Zeile aktualisiert wird
            // !isset($postData['field']) || empty($postData['field'])
        ) {
            $response['error'] = 'Missing required parameters (table, id) for updateData action.';
            return $response;
        }

        // Daten extrahieren, reservierte Schlüssel ausschließen
        $reservedKeys = ['action', 'table', 'id', 'field', 'is_reload'];
        $data = [];
        foreach ($postData as $key => $value) {
            if (!in_array($key, $reservedKeys)) {
                $data[$key] = $value; // Hier keine weitere Filterung nach 'field', updateData kümmert sich darum
            }
        }

        //if (empty($data)) {
        //    $response['error'] = 'No data provided to save.';
        //    return $response;
        //}


        try {
            // Annahme: updateData kann entweder spezifische Felder oder alle übergebenen $data aktualisieren
            // Der Parameter 'field' aus $postData wird hier nicht direkt an updateData übergeben,
            // da die Logik alle nicht-reservierten Keys als Daten nimmt.
            // Passe dies an, falls dein updateData explizit $postData['field'] erwartet.
            $success = $this->dbManager->updateMergeData($postData['id'], $postData['table'], $postData['field'] , $data); // $postData['field'] hier entfernt, $data enthält alles

            if ($success) {
                $response['success'] = 1;
            } else {
                $response['error'] = 'Failed to update data. Record might not exist or data unchanged.';
            }
        } catch (Exception $e) {
            // Log error $e->getMessage();
            $response['error'] = 'Database error during updateData action: ' . $e->getMessage();
        }

        return $response;
    }

    public function handleUpdateFields($postData) {

        $response['success'] = 0;

        // Prüfe, ob alle notwendigen Parameter vorhanden sind
        if (!isset($postData['id'], $postData['table'], $postData['fields'])) {
            return false;
        }

        $id = $postData['id'];
        $table = $postData['table'];

        // Wandelt den kommaseparierten String in ein Array um und trimmt Leerzeichen
        if( ! is_array($postData['fields']) ) {
            $fields = array_map('trim', explode(',', $postData['fields']));
        }else {
            $fields = $postData['fields'];
        }

        // Entferne die Steuerparameter, damit $data nur die zu aktualisierenden Felder enthält
        $data = $postData;
        unset($data['id'], $data['table'], $data['fields']);

        $success = $this->dbManager->updateFields($id, $table, $fields, $data);

        if( $success ) {
            $response['success'] = 1;
        }

        // Aufruf der updateFields-Methode vom dbManager
        return $response;
    }

    public function outputResponse($response)
    {
        // Gebe die finale Antwort als JSON aus
        header('Content-Type: application/json');
        // Stelle sicher, dass $response auch die Daten von handleRequest enthält
        // $response wird von handleRequest überschrieben, wenn alles gut geht.

        if( ! isset($response['success']) ) {
            $response['success'] = 0;
        }

        echo json_encode($response);
    }

    /**
     * Löscht eine Zeile anhand ihrer ID.
     *
     * @param array $postData Daten aus der POST-Anfrage. Erwartet ['table', 'id'].
     * @return array Response-Array mit 'success' oder 'error'.
     */
    private function handleDelete(array $postData): array {
        $response = ['success' => 0];

        if (!isset($postData['table']) || empty($postData['table'])) {
            $response['error'] = 'Table parameter is missing for delete action.';
            return $response;
        }
        if (!isset($postData['id']) || empty($postData['id'])) {
            $response['error'] = 'ID parameter is missing for delete action.';
            return $response;
        }

        try {
            $deleted = $this->dbManager->deleteById($postData['id'], $postData['table']);
            if ($deleted) { // Annahme: deleteById gibt true bei Erfolg zurück
                $response['success'] = 1;
            } else {
                $response['error'] = 'Failed to delete row. Record might not exist.';
            }
        } catch (Exception $e) {
            // Log error $e->getMessage();
            $response['error'] = 'Database error during delete action: ' . $e->getMessage();
        }

        return $response;
    }

    /**
     * Schaltet den Zustand eines Feldes um (z.B. boolean).
     *
     * @param array $postData Daten aus der POST-Anfrage. Erwartet ['table', 'id', 'field'].
     * @return array Response-Array mit 'success', 'new_state' oder 'error'.
     */
    private function handleToggle(array $postData): array {
        $response = ['success' => 0];

        if (!isset($postData['table']) || empty($postData['table'])) {
            $response['error'] = 'Table parameter is missing for toggle action.';
            return $response;
        }
        if (!isset($postData['id']) || empty($postData['id'])) {
            $response['error'] = 'ID parameter is missing for toggle action.';
            return $response;
        }
        if (!isset($postData['field']) || empty($postData['field'])) {
            $response['error'] = 'Field parameter is missing for toggle action.';
            return $response;
        }

        try {
            // toggleField sollte den neuen Zustand zurückgeben
            $newState = $this->dbManager->toggleField($postData['id'], $postData['table'], $postData['field']);

            // Prüfen, ob der Zustand erfolgreich geändert wurde (nicht null oder false, je nach Implementierung)
            if ($newState !== null && $newState !== false) {
                $response['new_state'] = $newState;
                $response['success'] = 1;
            } else {
                $response['error'] = 'Failed to toggle field state. Record or field might not exist.';
            }
        } catch (Exception $e) {
            // Log error $e->getMessage();
            $response['error'] = 'Database error during toggle action: ' . $e->getMessage();
        }

        return $response;
    }


    /**
     * Führt eine Methode einer Klasse aus, basierend auf den POST-Parametern.
     *
     * @param array $postData Daten aus der POST-Anfrage. Erwartet ['class', 'method', 'parameters'].
     * @return array Response-Array mit 'success', 'result' oder 'error'.
     */
    private function handleExecute(array $postData): array
    {
        $response = ['success' => 0];

        try {
            if (!isset($postData['class']) || !isset($postData['method'])) {
                throw new InvalidArgumentException('Class or method not specified.');
            }

            $className = $postData['class'];
            $methodName = $postData['method'];

            //if (!class_exists($className)) {
            //    // Versuchen, die Klasse zu laden
            //    $classFile = BASE_PATH . 'classes/' . strtolower($className) . '.php';
            //    if (file_exists($classFile)) {
            //        require_once $classFile;
            //    } else {
            //        throw new RuntimeException("Class '$className' not found.");
            //    }
            //}

            // Instanz der Klasse erstellen
            $instance = new $className();

            if (!method_exists($instance, $methodName)) {
                throw new RuntimeException("Method '$methodName' does not exist in class '$className'.");
            }

            // Remove class and method keys, the rest are parameters
            $params = $postData;
            unset($params['class'], $params['method']);

            $refMethod = new ReflectionMethod($className, $methodName);
            $orderedParams = [];

            foreach ($refMethod->getParameters() as $param) {
                $name = $param->getName();
                if (array_key_exists($name, $params)) {
                    $orderedParams[] = $params[$name];
                } elseif ($param->isOptional()) {
                    $orderedParams[] = $param->getDefaultValue();
                } else {
                    throw new RuntimeException("Missing required parameter: '$name'");
                }
            }

            $result = $refMethod->invokeArgs($instance, $orderedParams);
            $response['success'] = 1;
            $response['result'] = $result;

        } catch (Throwable $e) {
            $response['error'] = $e->getMessage();
        }

        return $response;
    }

}