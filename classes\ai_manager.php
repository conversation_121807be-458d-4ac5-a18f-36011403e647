<?php

/**
 * AI Manager Class
 *
 * A wrapper class for managing different AI providers (Gemini, OpenAI, and OpenRouter)
 * Provides a unified interface for generating AI content
 */
class AiManager
{
    private ?GeminiAiManager $geminiManager = null;
    private ?OpenaiManager $openaiManager = null;
    private ?OpenRouterManager $openrouterManager = null;
    private string $defaultProvider = 'openai';
    private string $geminiModel = 'gemini-2.5-pro-exp-03-25';
    private string $openaiModel = 'gpt-4o-mini';
    private string $openrouterModel = 'anthropic/claude-3-opus:beta';
    private float $openaiTemperature = 0.7;
    private float $openrouterTemperature = 0.7;
    private int $openaiMaxTokens = 10000;
    private int $openrouterMaxTokens = 10000;

    /**
     * Constructor
     *
     * Initializes the AI Manager with configuration from ConfigManager
     */
    public function __construct()
    {
        // Get AI configuration from ConfigManager
        $aiConfig = ConfigManager::get('ai');

        if (!$aiConfig) {
            // If no AI config is found, we can't proceed
            return;
        }

        // Set default provider
        if (!empty($aiConfig['default_provider']) &&
            in_array($aiConfig['default_provider'], ['openai', 'gemini', 'openrouter'])) {
            $this->defaultProvider = $aiConfig['default_provider'];
        }

        // Initialize OpenAI if configured
        if (!empty($aiConfig['providers']['openai']['api_key'])) {
            $openaiConfig = $aiConfig['providers']['openai'];

            // Set model if provided
            if (!empty($openaiConfig['model'])) {
                $this->openaiModel = $openaiConfig['model'];
            }

            // Set temperature if provided
            if (isset($openaiConfig['temperature'])) {
                $this->openaiTemperature = (float)$openaiConfig['temperature'];
            }

            // Set max tokens if provided
            if (isset($openaiConfig['max_tokens'])) {
                $this->openaiMaxTokens = (int)$openaiConfig['max_tokens'];
            }

            // Initialize OpenAI manager
            $this->openaiManager = new OpenaiManager($openaiConfig['api_key']);
            $this->openaiManager->setModel($this->openaiModel);
            $this->openaiManager->setTemperature($this->openaiTemperature);
            $this->openaiManager->setMaxTokens($this->openaiMaxTokens);
        }

        // Initialize Gemini if configured
        if (!empty($aiConfig['providers']['gemini']['api_key'])) {
            $geminiConfig = $aiConfig['providers']['gemini'];

            // Set model if provided
            if (!empty($geminiConfig['model'])) {
                $this->geminiModel = $geminiConfig['model'];
            }

            // Build the base URL with the specified model
            $baseUrl = $this->buildGeminiBaseUrl($this->geminiModel);
            $this->geminiManager = new GeminiAiManager($geminiConfig['api_key'], $baseUrl);
        }

        // Initialize OpenRouter if configured
        if (!empty($aiConfig['providers']['openrouter']['api_key'])) {
            $openrouterConfig = $aiConfig['providers']['openrouter'];

            // Set model if provided
            if (!empty($openrouterConfig['model'])) {
                $this->openrouterModel = $openrouterConfig['model'];
            }

            // Set temperature if provided
            if (isset($openrouterConfig['temperature'])) {
                $this->openrouterTemperature = (float)$openrouterConfig['temperature'];
            }

            // Set max tokens if provided
            if (isset($openrouterConfig['max_tokens'])) {
                $this->openrouterMaxTokens = (int)$openrouterConfig['max_tokens'];
            }

            // Get HTTP referer and site URL if provided
            $httpReferer = $openrouterConfig['http_referer'] ?? '';
            $httpSiteUrl = $openrouterConfig['http_site_url'] ?? '';

            // Initialize OpenRouter manager
            $this->openrouterManager = new OpenRouterManager(
                $openrouterConfig['api_key'],
                $httpReferer,
                $httpSiteUrl
            );
            $this->openrouterManager->setModel($this->openrouterModel);
            $this->openrouterManager->setTemperature($this->openrouterTemperature);
            $this->openrouterManager->setMaxTokens($this->openrouterMaxTokens);
        }

        // Fall back to available provider if the default is not available
        if ($this->defaultProvider === 'openai' && !$this->openaiManager) {
            if ($this->geminiManager) {
                $this->defaultProvider = 'gemini';
            } elseif ($this->openrouterManager) {
                $this->defaultProvider = 'openrouter';
            }
        } elseif ($this->defaultProvider === 'gemini' && !$this->geminiManager) {
            if ($this->openaiManager) {
                $this->defaultProvider = 'openai';
            } elseif ($this->openrouterManager) {
                $this->defaultProvider = 'openrouter';
            }
        } elseif ($this->defaultProvider === 'openrouter' && !$this->openrouterManager) {
            if ($this->openaiManager) {
                $this->defaultProvider = 'openai';
            } elseif ($this->geminiManager) {
                $this->defaultProvider = 'gemini';
            }
        }
    }

    /**
     * Build the Gemini API base URL with the specified model
     *
     * @param string $model The Gemini model to use
     * @return string The complete base URL
     */
    private function buildGeminiBaseUrl(string $model): string
    {
        return "https://generativelanguage.googleapis.com/v1beta/models/{$model}:generateContent";
    }

    /**
     * Generate content using the specified or default AI provider
     *
     * @param string $prompt The prompt to send to the AI
     * @param string $systemPrompt The system prompt (for OpenAI and OpenRouter)
     * @param string|null $provider The AI provider to use ('openai', 'gemini', or 'openrouter')
     * @return string|null The generated content or null if generation failed
     * @throws Exception If the specified provider is not available
     */
    public function generateContent(string $prompt, string $systemPrompt = '', $provider = null): ?string
    {
        // Use default provider if none specified
        $provider = $provider ?? $this->defaultProvider;

        // Generate content based on provider
        switch ($provider) {
            case 'openai':
                if (!$this->openaiManager) {
                    throw new Exception('OpenAI manager is not initialized');
                }
                return $this->openaiManager->createChatCompletion($prompt, $systemPrompt);

            case 'gemini':
                if (!$this->geminiManager) {
                    throw new Exception('Gemini AI manager is not initialized');
                }
                // Note: Gemini doesn't use system prompts, so we prepend it to the prompt if provided
                $fullPrompt = $systemPrompt ? "$prompt\n\n$systemPrompt" : $prompt;

                return $this->geminiManager->generateContent($fullPrompt);

            case 'openrouter':
                if (!$this->openrouterManager) {
                    throw new Exception('OpenRouter manager is not initialized');
                }
                return $this->openrouterManager->createChatCompletion($prompt, $systemPrompt);

            default:
                throw new Exception("Unknown AI provider: $provider");
        }
    }

    /**
     * Set the model for the current default provider or a specified provider
     *
     * @param string $model The model to use
     * @param string|null $provider The provider to set the model for (null for default provider)
     * @return $this For method chaining
     * @throws Exception If the specified provider is not initialized
     */
    public function setModel(string $model, ?string $provider = null): self
    {
        // Use default provider if none specified
        $provider = $provider ?? $this->defaultProvider;

        switch ($provider) {
            case 'openai':
                if (!$this->openaiManager) {
                    throw new Exception('OpenAI manager is not initialized');
                }
                $this->openaiModel = $model;
                $this->openaiManager->setModel($model);
                break;

            case 'gemini':
                if (!$this->geminiManager) {
                    throw new Exception('Gemini AI manager is not initialized');
                }
                // Use the existing setGeminiModel method
                $this->setGeminiModel($model);
                break;

            case 'openrouter':
                if (!$this->openrouterManager) {
                    throw new Exception('OpenRouter manager is not initialized');
                }
                // Use the existing setOpenRouterModel method
                $this->setOpenRouterModel($model);
                break;

            default:
                throw new Exception("Unknown AI provider: $provider");
        }

        return $this;
    }

    /**
     * Get the current OpenAI model
     *
     * @return string The current OpenAI model
     */
    public function getOpenaiModel(): string
    {
        return $this->openaiModel;
    }

    /**
     * Set the temperature for the current default provider or a specified provider
     *
     * @param float $temperature The temperature value
     * @param string|null $provider The provider to set the temperature for (null for default provider)
     * @return $this For method chaining
     * @throws Exception If the specified provider is not initialized
     */
    public function setTemperature(float $temperature, ?string $provider = null): self
    {
        // Use default provider if none specified
        $provider = $provider ?? $this->defaultProvider;

        switch ($provider) {
            case 'openai':
                if (!$this->openaiManager) {
                    throw new Exception('OpenAI manager is not initialized');
                }
                $this->openaiTemperature = $temperature;
                $this->openaiManager->setTemperature($temperature);
                break;

            case 'openrouter':
                if (!$this->openrouterManager) {
                    throw new Exception('OpenRouter manager is not initialized');
                }
                // Use the existing setOpenRouterTemperature method
                $this->setOpenRouterTemperature($temperature);
                break;

            case 'gemini':
                // Gemini doesn't have a setTemperature method, so we just throw an exception
                throw new Exception('Setting temperature is not supported for Gemini AI');

            default:
                throw new Exception("Unknown AI provider: $provider");
        }

        return $this;
    }

    /**
     * Get the current OpenAI temperature
     *
     * @return float The current OpenAI temperature
     */
    public function getOpenaiTemperature(): float
    {
        return $this->openaiTemperature;
    }

    /**
     * Set the max tokens for the current default provider or a specified provider
     *
     * @param int $maxTokens The maximum number of tokens
     * @param string|null $provider The provider to set the max tokens for (null for default provider)
     * @return $this For method chaining
     * @throws Exception If the specified provider is not initialized
     */
    public function setMaxTokens(int $maxTokens, ?string $provider = null): self
    {
        // Use default provider if none specified
        $provider = $provider ?? $this->defaultProvider;

        switch ($provider) {
            case 'openai':
                if (!$this->openaiManager) {
                    throw new Exception('OpenAI manager is not initialized');
                }
                $this->openaiMaxTokens = $maxTokens;
                $this->openaiManager->setMaxTokens($maxTokens);
                break;

            case 'openrouter':
                if (!$this->openrouterManager) {
                    throw new Exception('OpenRouter manager is not initialized');
                }
                // Use the existing setOpenRouterMaxTokens method
                $this->setOpenRouterMaxTokens($maxTokens);
                break;

            case 'gemini':
                // Gemini doesn't have a setMaxTokens method, so we just throw an exception
                throw new Exception('Setting max tokens is not supported for Gemini AI');

            default:
                throw new Exception("Unknown AI provider: $provider");
        }

        return $this;
    }

    /**
     * Get the current OpenAI max tokens
     *
     * @return int The current OpenAI max tokens
     */
    public function getOpenaiMaxTokens(): int
    {
        return $this->openaiMaxTokens;
    }

    /**
     * Get the OpenAI manager instance
     *
     * @return OpenaiManager|null The OpenAI manager instance or null if not initialized
     */
    public function getOpenaiManager(): ?OpenaiManager
    {
        return $this->openaiManager;
    }

    /**
     * Get the Gemini AI manager instance
     *
     * @return GeminiAiManager|null The Gemini AI manager instance or null if not initialized
     */
    public function getGeminiManager(): ?GeminiAiManager
    {
        return $this->geminiManager;
    }

    /**
     * Set the Gemini model and update the base URL
     *
     * @param string $model The Gemini model to use
     * @return $this For method chaining
     * @throws Exception If Gemini manager is not initialized
     */
    public function setGeminiModel(string $model): self
    {
        if (!$this->geminiManager) {
            throw new Exception('Gemini AI manager is not initialized');
        }

        $this->geminiModel = $model;

        // Create a new Gemini manager with the updated base URL
        $apiKey = $this->geminiManager->getApiKey();
        $baseUrl = $this->buildGeminiBaseUrl($model);
        $this->geminiManager = new GeminiAiManager($apiKey, $baseUrl);

        return $this;
    }

    /**
     * Get the current Gemini model
     *
     * @return string The current Gemini model
     */
    public function getGeminiModel(): string
    {
        return $this->geminiModel;
    }

    /**
     * Set the default AI provider
     *
     * @param string $provider The provider to set as default ('openai', 'gemini', or 'openrouter')
     * @return $this For method chaining
     * @throws Exception If the provider is invalid
     */
    public function setDefaultProvider(string $provider): self
    {
        if (!in_array($provider, ['openai', 'gemini', 'openrouter'])) {
            throw new Exception("Invalid AI provider: $provider");
        }
        $this->defaultProvider = $provider;
        return $this;
    }

    /**
     * Get the current default AI provider
     *
     * @return string The current default provider ('openai', 'gemini', or 'openrouter')
     */
    public function getDefaultProvider(): string
    {
        return $this->defaultProvider;
    }

    /**
     * Set the model for OpenRouter
     *
     * @param string $model The model to use
     * @return $this For method chaining
     * @throws Exception If OpenRouter manager is not initialized
     */
    public function setOpenRouterModel(string $model): self
    {
        if (!$this->openrouterManager) {
            throw new Exception('OpenRouter manager is not initialized');
        }
        $this->openrouterModel = $model;
        $this->openrouterManager->setModel($model);
        return $this;
    }

    /**
     * Get the current OpenRouter model
     *
     * @return string The current OpenRouter model
     */
    public function getOpenRouterModel(): string
    {
        return $this->openrouterModel;
    }

    /**
     * Set the temperature for OpenRouter
     *
     * @param float $temperature The temperature value
     * @return $this For method chaining
     * @throws Exception If OpenRouter manager is not initialized
     */
    public function setOpenRouterTemperature(float $temperature): self
    {
        if (!$this->openrouterManager) {
            throw new Exception('OpenRouter manager is not initialized');
        }
        $this->openrouterTemperature = $temperature;
        $this->openrouterManager->setTemperature($temperature);
        return $this;
    }

    /**
     * Get the current OpenRouter temperature
     *
     * @return float The current OpenRouter temperature
     */
    public function getOpenRouterTemperature(): float
    {
        return $this->openrouterTemperature;
    }

    /**
     * Set the max tokens for OpenRouter
     *
     * @param int $maxTokens The maximum number of tokens
     * @return $this For method chaining
     * @throws Exception If OpenRouter manager is not initialized
     */
    public function setOpenRouterMaxTokens(int $maxTokens): self
    {
        if (!$this->openrouterManager) {
            throw new Exception('OpenRouter manager is not initialized');
        }
        $this->openrouterMaxTokens = $maxTokens;
        $this->openrouterManager->setMaxTokens($maxTokens);
        return $this;
    }

    /**
     * Get the current OpenRouter max tokens
     *
     * @return int The current OpenRouter max tokens
     */
    public function getOpenRouterMaxTokens(): int
    {
        return $this->openrouterMaxTokens;
    }

    /**
     * Get the OpenRouter manager instance
     *
     * @return OpenRouterManager|null The OpenRouter manager instance or null if not initialized
     */
    public function getOpenRouterManager(): ?OpenRouterManager
    {
        return $this->openrouterManager;
    }
}
