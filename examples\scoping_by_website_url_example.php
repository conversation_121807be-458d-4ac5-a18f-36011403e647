<?php

// Include necessary files
require_once '../app/_base/base.php';
require_once '../classes/scoping_manager.php';

// Create a ScopingManager instance
$scopingManager = new ScopingManager();

echo "=== Scoping Manager Add By Website URL Example ===\n\n";

// Example 1: Add a scoping entry for an existing website
echo "Example 1: Add a scoping entry for an existing website\n";

// Replace with a valid website URL from your database
$websiteUrl = 'https://example.com';

// Add a scoping entry with minimal data
$result = $scopingManager->addScopingByWebsiteUrl($websiteUrl);

if ($result['success']) {
    echo "Success! Scoping entry added with ID: {$result['last_id']}\n";
    
    if (isset($result['project_exists']) && $result['project_exists']) {
        echo "Used existing project with ID: {$result['project_id']}\n";
    } else if (isset($result['project_created']) && $result['project_created']) {
        echo "Created new project with ID: {$result['project_id']}\n";
    }
    
    echo "Project ID: {$result['scoping_data']['project_id']}\n\n";
} else {
    echo "Error: {$result['error']}\n\n";
}

// Example 2: Add a scoping entry with options and text for a new website
echo "Example 2: Add a scoping entry with options and text for a new website\n";

// Use a unique website URL to ensure a new project is created
$websiteUrl = 'https://example-' . time() . '.com';

// Sample options data
$options = [
    'scope_type' => 'website',
    'priority' => 'high',
    'estimated_hours' => 20
];

// Sample text data
$text = "This project requires a complete website redesign with focus on mobile responsiveness and SEO optimization.";

// Add a scoping entry with options and text
$result = $scopingManager->addScopingByWebsiteUrl($websiteUrl, $options, $text);

if ($result['success']) {
    echo "Success! Scoping entry added with ID: {$result['last_id']}\n";
    
    if (isset($result['project_exists']) && $result['project_exists']) {
        echo "Used existing project with ID: {$result['project_id']}\n";
    } else if (isset($result['project_created']) && $result['project_created']) {
        echo "Created new project with ID: {$result['project_id']}\n";
        echo "Project website URL: {$result['project_data']['website_url']}\n";
    }
    
    echo "Project ID: {$result['scoping_data']['project_id']}\n";
    echo "Options: " . print_r(json_decode($result['scoping_data']['options_data'], true), true) . "\n";
    echo "Text: {$result['scoping_data']['text_data']}\n\n";
} else {
    echo "Error: {$result['error']}\n\n";
}
