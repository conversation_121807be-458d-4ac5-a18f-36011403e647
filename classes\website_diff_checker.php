<?php

/**
 * Website Diff Checker Class
 *
 * This class handles website content comparison using a state-based workflow.
 * It manages entries, crawling, diff creation, and AI summarization.
 */
class WebsiteDiffChecker
{
    private $dbManager; // Database manager instance
    private $logFilePath; // Path for the log file

    /**
     * Constructor
     * Initializes the database manager from the global scope.
     */
    public function __construct()
    {
        global $dbManager; // Access the global variable
        if (isset($dbManager) && is_object($dbManager)) {
            $this->dbManager = $dbManager; // Assign it to the class property
        } else {
            // Throw an exception or log a fatal error if dbManager isn't available
            // This is crucial as the class relies heavily on it.
            // Since logging is removed, we'll throw an exception.
            throw new Exception("Global \$dbManager is not set or is not an object. WebsiteDiffChecker cannot function.");
        }

        // Initialize Log Path
        $logDir = BASE_PATH . 'logs/';
        // Ensure the log directory exists (with basic error handling)
        if (!is_dir($logDir)) {
            if (!mkdir($logDir, 0777, true)) {
                // Cannot create log dir, logging will fail silently or use $this->_logMessage fallback
                $this->_logMessage("CRITICAL: Failed to create log directory: {$logDir}");
                $this->logFilePath = null; // Indicate logging is unavailable
            } else {
                $this->logFilePath = $logDir . 'website_diff_' . date('Y-m-d') . '.log';
            }
        } else {
            $this->logFilePath = $logDir . 'website_diff_' . date('Y-m-d') . '.log';
        }
    }

    /**
     * Add a new entry in the website_diff_checker table and populate pages.
     * This is the starting point for a new diff process for a project.
     *
     * @param int $projectId The project ID
     * @return int|false The ID of the new entry or false on failure
     */
    public function addEntry(int $projectId)
    {
        // No need for 'global $dbManager;' here anymore, use $this->dbManager

        try {
            // Check if there's already an unfinished entry for this project
            $existingEntries = $this->dbManager->findByValues(
                [
                    'project_id' => $projectId,
                    'is_crawl_finished' => 0 // Check if overall process is finished
                ],
                'website_diff_checker'
            );

            // If there's already an unfinished entry, return false
            if (!empty($existingEntries)) {
                $this->_logMessage("there's already an unfinished entry");
                return false;
            }

            // Get the project to find the website_url
            $project = $this->dbManager->findById($projectId, 'projects');
            if (empty($project) || empty($project['website_url'])) {
                $this->_logMessage("project not found or webseiturl is empty");
                return false;
            }

            // Get internal links for the website (using helper)
            $websiteUrl = $project['website_url'];
            $internalLinks = $this->_findInternalLinksByWebsiteUrl($websiteUrl);

            if (empty($internalLinks)) {
                $this->_logMessage("no internal links");
                return false;
            }

            //print_r($internalLinks);

            // Prepare data for insertion (main entry)
            $data = [
                'project_id' => $projectId,
                'website_url' => $project['website_url'],
                'is_crawl_finished' => 0,
                'is_diff_summary_finished' => 0,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];


            // Insert the new entry
            $newId = $this->dbManager->insertNewRowByData('website_diff_checker', $data);

            if ($newId) {
                // Add pages to the website_diff_checker_pages table
                foreach ($internalLinks as $url) {
                    $pageData = [
                        'project_id' => $projectId,
                        'website_diff_checker_id' => $newId,
                        'page_url' => $url,
                        'is_crawl_finished' => 0,
                        'is_diff_finished' => 0,
                        'is_diff_summary_finished' => 0,
                        'created_at' => date('Y-m-d H:i:s'),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                    $this->dbManager->insertNewRowByData('website_diff_checker_pages', $pageData);
                }
            } else {
                // Handle insertion failure if necessary
            }

            return $newId;
        } catch (Exception $e) {
            // Handle exception (e.g., log to general error log if available)
            $this->_logMessage("Error adding website_diff_checker entry: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Handles the entire diff process (crawl, diff, summarize) for a single project entry.
     * This method is called by the public 'handle' method.
     * It processes the *latest* entry that is not yet fully finished.
     *
     * @param int $projectId The project ID to process.
     * @return bool True if processing finished or nothing to process, false on critical error.
     */
    public function handleByProjectId(int $projectId): bool
    {
        // No need for 'global $dbManager;' here anymore, use $this->dbManager

        try {
            // 1. Find the latest entry for this project that is not yet fully summarized
            $entries = $this->dbManager->findByValues(
                ['project_id' => $projectId, 'is_diff_summary_finished' => 0],
                'website_diff_checker',
                'created_at DESC',
                1
            );

            if (empty($entries)) {
                return true; // Nothing to do
            }

            $entry = $entries[0];
            $entryId = $entry['id'];

            // 2. Perform Crawling if not finished
            if (!$entry['is_crawl_finished']) {
                $crawlSuccess = $this->_handleCrawlStep($entry);
                if (!$crawlSuccess) {
                    return false; // Indicate failure
                }
                // Re-fetch entry after crawl might update it
                $entry = $this->dbManager->findById($entryId, 'website_diff_checker');
                if (empty($entry)) {
                    $this->_logMessage("Entry ID {$entryId} disappeared after crawl?");
                    return false; // Critical error
                }
            }

            // 3. Perform Page Diffing if crawling finished but diffing is not
            if ($entry['is_crawl_finished'] && !$entry['is_diff_finished']) {

                $diffResult = $this->_handlePageDiffStep($entry);
                if ($diffResult['pages_failed'] > 0) {
                    // Decide if partial failure should halt or just be noted
                }

                // Check if ALL pages for this entry are now diff_finished
                if ($this->_areAllPagesDiffFinished($entryId)) {
                    $this->_updateMainEntryStatus($entryId, ['is_diff_finished' => 1]);
                    // Re-fetch entry status
                    $entry = $this->dbManager->findById($entryId, 'website_diff_checker');
                    if (empty($entry)) { /* critical error */ return false; }
                }
            }


            // 4. Perform AI Page Summarization if diffing finished but summarization is not
            if ($entry['is_diff_finished'] && !$entry['is_diff_summary_finished']) {
                $summaryResult = $this->_handlePageSummaryStep($entryId);
                if ($summaryResult['pages_failed'] > 0) {
                    // Decide if partial failure should halt or just be noted
                }

                // Check if ALL diff_finished pages for this entry are now summary_finished
                $this->_logMessage('Check if ALL diff_finished pages for this entry are now summary_finished');
                if ($this->_areAllRelevantPagesSummaryFinished($entryId)) {
                    $this->_logMessage('All Relevant Pages Summary is Finished!');
                    // 5. Create Project Summary if all page summaries are finished
                    $projectSummarySuccess = $this->_handleProjectSummaryStep($entryId);
                    if ($projectSummarySuccess) {
                        $this->_updateMainEntryStatus($entryId, ['is_diff_summary_finished' => 1]);
                        // Processing for this entry is now complete.
                    } else {
                        return false; // Indicate failure
                    }
                }
            }

            // If we reached here, it means we either finished a step or were waiting for other parts
            // to finish. If the main entry is now fully summarized, we return true.
            $finalEntryStatus = $this->dbManager->findById($entryId, 'website_diff_checker');
            return ($finalEntryStatus && $finalEntryStatus['is_diff_summary_finished'] == 1);


        } catch (Exception $e) {
            $this->_logMessage("Critical error handling project ID {$projectId}: " . $e->getMessage());
            return false; // Indicate failure
        }
    }

    /**
     * Check if the last crawl from a defined project has finished crawling
     *
     * @param int $projectId The project ID
     * @return bool True if the last crawl is finished, false otherwise
     */
    public function isLastCrawlFinished(int $projectId): bool
    {
        $lastCrawl = $this->getLastCrawlByProjectId($projectId);

        if ($lastCrawl === null) {
            return false; // No crawl found
        }

        return (bool)$lastCrawl['is_crawl_finished'];
    }

    /**
     * Get HTML content of a specific page from the last crawl's zip file
     * Unzips the last zip, finds the page by name, returns content, and tidies up
     *
     * @param int $projectId The project ID
     * @param string $pageName The name of the page file to retrieve (e.g., 'homepage.html')
     * @return string|false The HTML content of the page or false on failure
     */
    public function getLastCrawledPageHtmlByName(int $projectId, string $pageName)
    {
        try {
            // Get the last crawl entry
            $lastCrawl = $this->getLastCrawlByProjectId($projectId);

            if ($lastCrawl === null) {
                $this->_logMessage("No crawl found for project {$projectId}");
                return false;
            }

            // Check if crawl is finished
            if (!$lastCrawl['is_crawl_finished']) {
                $this->_logMessage("Last crawl for project {$projectId} is not finished yet");
                return false;
            }

            // Check if zip file exists
            if (empty($lastCrawl['zip_file_path']) || !file_exists($lastCrawl['zip_file_path'])) {
                $this->_logMessage("Zip file not found for project {$projectId}: " . ($lastCrawl['zip_file_path'] ?? 'null'));
                return false;
            }

            // Create temporary extraction directory
            $tempDir = BASE_PATH . 'temp/website_diff_checker/extract_' . $lastCrawl['id'] . '_' . uniqid();

            // Extract the zip file
            $extractedFiles = $this->_extractZipArchive($lastCrawl['zip_file_path'], $tempDir);

            if ($extractedFiles === false) {
                $this->_logMessage("Failed to extract zip file for project {$projectId}");
                return false;
            }

            // Look for the requested page file
            $pageFilePath = null;
            foreach ($extractedFiles as $fileName => $filePath) {
                if ($fileName === $pageName) {
                    $pageFilePath = $filePath;
                    break;
                }
            }

            if ($pageFilePath === null) {
                $this->_logMessage("Page '{$pageName}' not found in zip for project {$projectId}");
                $this->_cleanupDirectory($tempDir, true);
                return false;
            }

            // Read the HTML content
            $htmlContent = file_get_contents($pageFilePath);

            if ($htmlContent === false) {
                $this->_logMessage("Failed to read content from '{$pageName}' for project {$projectId}");
                $this->_cleanupDirectory($tempDir, true);
                return false;
            }

            // Clean up temporary directory
            $this->_cleanupDirectory($tempDir, true);

            $this->_logMessage("Successfully retrieved content for '{$pageName}' from project {$projectId}");
            return $htmlContent;

        } catch (Exception $e) {
            $this->_logMessage("Exception in getLastCrawledPageHtmlByName for project {$projectId}, page '{$pageName}': " . $e->getMessage());

            // Clean up if temp directory was created
            if (isset($tempDir) && is_dir($tempDir)) {
                $this->_cleanupDirectory($tempDir, true);
            }

            return false;
        }
    }

    /**
     * Entry point for cron job. Finds projects that need processing and calls handleByProjectId.
     *
     * @return void
     */
    public function handle(): void
    {
        // No need for 'global $dbManager;' here anymore, use $this->dbManager

        try {
            // Find distinct project IDs that have at least one entry that is not yet fully summarized
            $sql = "SELECT DISTINCT project_id FROM website_diff_checker WHERE is_diff_summary_finished = 0";
            $stmt = $this->dbManager->getPDO()->query($sql);
            $projectIds = $stmt->fetchAll(PDO::FETCH_COLUMN) ?: [];

            if (empty($projectIds)) {
                return;
            }

            foreach ($projectIds as $projectId) {
                $this->handleByProjectId((int)$projectId); // Process one project at a time
            }

        } catch (Exception $e) {
            $this->_logMessage("Error in main handle() loop: " . $e->getMessage());
        }
    }

    // --- Private Helper Methods ---

    /**
     * Handles the crawling step for a specific entry.
     * Stores the actual saved filename in the database.
     *
     * @param array $entry The website_diff_checker entry record.
     * @return bool True on success (crawl finished for this batch or entry), false on failure.
     */
    private function _handleCrawlStep(array $entry): bool
    {
        $scrapingAntApiToken = ConfigManager::get('scraping_ant.api_token');
        if (empty($scrapingAntApiToken)) {
            $this->_logMessage("ERROR: ScrapingAnt API token not found in configuration.");
            error_log("ERROR: ScrapingAnt API token not found in configuration."); // Log to error log as well
            return false;
        }

        $projectId = $entry['project_id'];
        $entryId = $entry['id'];
        $websiteUrl = $entry['website_url'];
        $domain = parse_url($websiteUrl, PHP_URL_HOST);
        if (empty($domain)) {
            $this->_logMessage("ERROR: Could not extract domain from website URL: {$websiteUrl}");
            error_log("ERROR: Could not extract domain from website URL: {$websiteUrl}");
            return false;
        }

        // Get pages for this entry that are not crawl_finished
        $pagesToCrawl = $this->dbManager->findByValues(
            ['website_diff_checker_id' => $entryId, 'is_crawl_finished' => 0],
            'website_diff_checker_pages'
        );

        if (empty($pagesToCrawl)) {
            $this->_logMessage("No uncrawled pages found for entry ID {$entryId}. Checking if all pages are marked finished...");
            if ($this->_areAllPagesCrawled($entryId)) {
                $this->_logMessage("All pages for entry {$entryId} are marked crawled. Marking main entry as crawl_finished.");
                // Ensure main entry status is updated if it wasn't already
                if (!$entry['is_crawl_finished']) {
                    $this->_updateMainEntryStatus($entryId, ['is_crawl_finished' => 1]);
                }
                return true; // Crawling is effectively finished
            }
            // This state might occur if pages were deleted after entry creation but before crawl
            $this->_logMessage("WARNING: Found 0 uncrawled pages for entry {$entryId}, and not all pages seem marked as finished in DB? Checking total page count.");
            $totalEntryPages = $this->dbManager->countByValues(['website_diff_checker_id' => $entryId], 'website_diff_checker_pages');
            if ($totalEntryPages == 0) {
                $this->_logMessage("Entry {$entryId} has 0 pages associated. Marking crawl as finished.");
                $this->_updateMainEntryStatus($entryId, ['is_crawl_finished' => 1]);
                return true;
            }
            // If there are pages, but none are marked is_crawl_finished=0, it implies they are all finished.
            // The _areAllPagesCrawled check should catch this, but logging the ambiguity.
            $this->_logMessage("Ambiguous state for entry {$entryId}. Assuming crawl is finished based on checks.");
            $this->_updateMainEntryStatus($entryId, ['is_crawl_finished' => 1]); // Mark finished to avoid loops
            return true;
        }

        $this->_logMessage("Found " . count($pagesToCrawl) . " pages to crawl for entry ID {$entryId}.");

        $tempDir = BASE_PATH . 'temp/website_diff_checker/' . $entryId;
        if (!file_exists($tempDir)) {
            if (!mkdir($tempDir, 0777, true)) {
                $this->_logMessage("ERROR: Failed to create temporary directory: {$tempDir}");
                error_log("ERROR: Failed to create temporary directory: {$tempDir}");
                return false;
            }
        }

        $crawledCount = 0;
        $failedCount = 0;
        $maxPagesPerBatch = 5; // Limit pages per execution to manage resources
        $this->_logMessage("Processing crawl batch (max {$maxPagesPerBatch} pages)...");

        foreach ($pagesToCrawl as $page) {
            if ($crawledCount >= $maxPagesPerBatch) {
                $this->_logMessage("Reached max pages per crawl batch ({$maxPagesPerBatch}). Stopping crawl for now.");
                break; // Stop processing pages in this batch
            }

            $url = $page['page_url'];
            $pageId = $page['id'];
            $this->_logMessage("Crawling page ID {$pageId}: {$url}");
            $savedFilename = null; // Initialize saved filename

            try {
                $crawler = new ScrapingAntCrawler($scrapingAntApiToken, $url, $domain);
                $crawler->addApiOptions('return_page_source', true);
                $crawler->fetch();
                $statusCode = $crawler->getStatusCode();
                $this->_logMessage("  Status code: {$statusCode}");

                $pageUpdateData = []; // Reset for each page

                if ($statusCode == 200) {
                    $html = $crawler->getHtml();
                    if ($html) {
                        // Generate initial safe filename
                        $urlPath = parse_url($url, PHP_URL_PATH); $urlQuery = parse_url($url, PHP_URL_QUERY);
                        $baseFilename = !empty($urlPath) && $urlPath !== '/' ? basename($urlPath) : 'index';
                        $baseFilename = pathinfo($baseFilename, PATHINFO_FILENAME); // Remove extension
                        $safeFilename = preg_replace('/[^a-zA-Z0-9_-]/', '_', $baseFilename); // Sanitize
                        if (!empty($urlQuery)) { $safeFilename .= '_' . substr(md5($urlQuery), 0, 8); } // Add query hash
                        $safeFilename = trim($safeFilename, '_') ?: 'index'; // Trim underscores and handle root

                        $filePath = $tempDir . '/' . $safeFilename . '.html'; // Add extension back

                        // Handle potential filename collisions gracefully
                        $counter = 1;
                        $originalFilePath = $filePath;
                        // Construct filename with suffix *before* the extension
                        $filenameWithoutExt = $tempDir . '/' . $safeFilename;
                        $extension = '.html';
                        while (file_exists($filePath)) {
                            $filePath = $filenameWithoutExt . '_' . $counter . $extension;
                            $counter++;
                            if ($counter > 100) { // Safety break
                                throw new Exception("Too many filename collisions for base {$safeFilename}.html");
                            }
                        }
                        // *** Store the final filename used ***
                        $savedFilename = basename($filePath);

                        if (file_put_contents($filePath, $html) !== false) {
                            $pageUpdateData['is_crawl_finished'] = 1;
                            // *** Add the saved filename to update data ***
                            $pageUpdateData['saved_filename'] = $savedFilename;
                            $crawledCount++;
                            $this->_logMessage("  Successfully crawled and saved: {$url} to {$filePath}");
                        } else {
                            $savedFilename = null; // Reset if save failed
                            throw new Exception("Failed to save HTML to file: {$filePath}");
                        }
                    } else {
                        throw new Exception("Empty HTML content received.");
                    }
                } else {
                    // Store non-200 status as an error, but mark crawl finished (we attempted)
                    $pageUpdateData['is_crawl_finished'] = 1; // Mark as attempted
                    $pageUpdateData['saved_filename'] = null; // No file saved
                    $pageUpdateData['diff_text'] = "Crawl Error: HTTP status code: {$statusCode}"; // Store status in diff_text
                    $failedCount++; // Count as failed for summary, but finished for process flow
                    $this->_logMessage("  Crawl attempted but failed (Status {$statusCode}). Marking finished with error.");
                    // throw new Exception("HTTP status code: {$statusCode}"); // Original behaviour - retries
                }

                // Update page status in DB
                // If successful crawl, $pageUpdateData contains is_crawl_finished=1 and saved_filename
                // If non-200 status, $pageUpdateData contains is_crawl_finished=1, saved_filename=null, diff_text=error
                if (!empty($pageUpdateData)) {
                    $this->_updatePageStatus($pageId, array_merge($pageUpdateData, ['updated_at' => date('Y-m-d H:i:s')]));
                }

            } catch (Exception $e) {
                $failedCount++;
                $this->_logMessage("  ERROR crawling page: {$url} - " . $e->getMessage());
                // Update page status with error, keep crawl unfinished to retry? Or mark finished with error?
                // Let's mark finished with error to avoid infinite retries on persistent issues.
                $this->_updatePageStatus($pageId, [
                    'diff_text' => 'Crawl Exception: ' . substr($e->getMessage(), 0, 500), // Store error
                    'saved_filename' => null, // Ensure filename is null on error
                    'is_crawl_finished' => 1, // Mark finished to prevent retry loops on exceptions
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
            }
        }

        $this->_logMessage("Crawl batch finished for entry {$entryId}. Crawled this run: {$crawledCount}, Failed/Errored this run: {$failedCount}.");

        // Check if all pages for this entry are now crawl_finished
        // This check is important AFTER the loop finishes or breaks
        if ($this->_areAllPagesCrawled($entryId)) {
            $this->_logMessage("All pages are now marked crawl_finished for entry ID {$entryId}. Proceeding to zip files.");
            $project = $this->dbManager->findById($projectId, 'projects');
            if (empty($project)) {
                $this->_logMessage("ERROR: Project {$projectId} not found when trying to zip files for entry {$entryId}.");
                error_log("ERROR: Project {$projectId} not found when trying to zip files for entry {$entryId}.");
                return false; // Cannot zip without project context
            }
            $zipFilePath = $this->_createZipArchive($entryId, $tempDir, $project['id']);
            $updateData = ['is_crawl_finished' => 1, 'updated_at' => date('Y-m-d H:i:s')];
            if ($zipFilePath) {
                $updateData['zip_file_path'] = $zipFilePath;
                $this->_logMessage("Created zip file: {$zipFilePath}");
                $this->_cleanupDirectory($tempDir, true);
                $this->_logMessage("Cleaned up temporary directory: {$tempDir}");
            } else {
                $this->_logMessage("ERROR: Failed to create zip file for entry ID {$entryId}. Crawl step cannot be marked fully complete.");
                error_log("ERROR: Failed to create zip file for entry ID {$entryId}.");
                // Update main entry with error note?
                $this->_updateMainEntryStatus($entryId, ['diff_summary_text' => 'Crawl finished but failed to create zip file.', 'is_crawl_finished' => 1]); // Mark finished to avoid loop
                return false; // Indicate failure, next step (diff) will fail anyway without zip
            }

            // Only update main entry if zip succeeded or was not needed (e.g., 0 pages)
            $this->_updateMainEntryStatus($entryId, $updateData);
            return true; // Crawl step fully completed successfully
        } else {
            $this->_logMessage("Crawl step partially completed for entry {$entryId}, more pages remain.");
            return true; // Indicate partial success for this run, needs more runs
        }
    }

    /**
     * Handles the page diffing step for a specific entry.
     * Assumes crawling is finished and zip files exist.
     * Uses explicit SQL to find the previous entry.
     * Uses 'saved_filename' from DB to find files in zips.
     * Only creates a diff if both previous and latest files exist.
     *
     * @param array $entry The website_diff_checker entry record (must have zip_file_path).
     * @return array Statistics {'pages_processed', 'pages_with_changes', 'pages_without_changes', 'pages_new', 'pages_failed'}
     */
    private function _handlePageDiffStep(array $entry): array
    {
        $stats = [
            'pages_processed' => 0,
            'pages_with_changes' => 0,
            'pages_without_changes' => 0,
            'pages_new' => 0,
            'pages_failed' => 0
        ];

        $entryId = $entry['id'];
        $projectId = $entry['project_id']; // Get project ID from current entry
        $this->_logMessage("Starting diff batch processing for entry ID {$entryId} (Project: {$projectId})...");

        // --- Find the previous completed entry using explicit SQL ---
        $previousEntry = null;
        try {
            $sql = "SELECT id, zip_file_path, created_at FROM website_diff_checker
                    WHERE project_id = :project_id
                      AND is_crawl_finished = 1
                      AND zip_file_path IS NOT NULL AND zip_file_path != '' -- Ensure previous entry has a zip
                      AND id < :entry_id
                    ORDER BY created_at DESC
                    LIMIT 1";

            $stmt = $this->dbManager->getPDO()->prepare($sql);
            if ($stmt) {
                $stmt->bindValue(':project_id', $projectId, PDO::PARAM_INT);
                $stmt->bindValue(':entry_id', $entryId, PDO::PARAM_INT);
                $stmt->execute();
                $previousEntry = $stmt->fetch(PDO::FETCH_ASSOC); // Fetch a single row
                if ($previousEntry === false) { $previousEntry = null; }
            } else {
                $this->_logMessage("ERROR: Failed to prepare SQL statement to find previous entry for entry ID {$entryId}.");
                error_log("ERROR: Failed to prepare SQL statement to find previous entry for entry ID {$entryId}.");
                return $stats; // Cannot proceed without knowing if there's a previous entry
            }
        } catch (PDOException $e) {
            $this->_logMessage("ERROR: Database error finding previous entry for entry ID {$entryId}: " . $e->getMessage());
            error_log("PDOException finding previous entry for entry ID {$entryId}: " . $e->getMessage());
            return $stats; // Cannot reliably proceed
        }
        // --- End of finding previous entry ---

        $this->_logMessage("Previous entry for comparison: " . ($previousEntry ? "ID {$previousEntry['id']} (Zip: {$previousEntry['zip_file_path']})" : "None found"));


        // Check zip files exist
        // We need the current entry's zip file regardless
        if (empty($entry['zip_file_path']) || !file_exists($entry['zip_file_path'])) {
            $this->_logMessage("ERROR: Latest entry #{$entryId} has no zip file ('{$entry['zip_file_path']}') or file doesn't exist. Cannot perform diff.");
            error_log("ERROR: Latest entry #{$entryId} has no zip file ('{$entry['zip_file_path']}') or file doesn't exist. Cannot perform diff.");
            return $stats; // Cannot proceed without the latest zip
        }
        // Check previous entry's zip only if previous entry exists
        if ($previousEntry) {
            // The SQL query already checked zip_file_path is not null/empty
            if (!file_exists($previousEntry['zip_file_path'])) {
                $this->_logMessage("WARNING: Previous entry #{$previousEntry['id']}'s zip file ('{$previousEntry['zip_file_path']}') not found on disk. Treating pages as 'new'.");
                error_log("WARNING: Previous entry #{$previousEntry['id']}'s zip file ('{$previousEntry['zip_file_path']}') not found on disk.");
                $previousEntry = null; // Treat as if no previous entry for comparison purposes
            }
        }

        // Create temporary directories for extraction
        $latestTempDir = null; $previousTempDir = null; $latestFiles = []; $previousFiles = [];

        // Extract latest zip (always needed)
        $latestTempDir = BASE_PATH . 'temp/website_diff_checker/diff_latest_' . $entryId;
        if (!file_exists($latestTempDir)) {
            if (!@mkdir($latestTempDir, 0777, true)) {
                if (!is_dir($latestTempDir)) { // Check again after potential race condition
                    $this->_logMessage("ERROR: Failed to create latest temp dir: {$latestTempDir}");
                    error_log("ERROR: Failed to create latest temp dir: {$latestTempDir}");
                    return $stats;
                }
            }
        }
        $this->_logMessage("Extracting latest zip file ('{$entry['zip_file_path']}') to temp dir: {$latestTempDir}");
        $latestFiles = $this->_extractZipArchive($entry['zip_file_path'], $latestTempDir);
        if ($latestFiles === false) {
            $this->_logMessage("ERROR: Failed to extract latest zip for entry ID {$entryId}. Cannot proceed.");
            error_log("ERROR: Failed to extract latest zip for entry ID {$entryId} from {$entry['zip_file_path']}");
            $this->_cleanupDirectory($latestTempDir, true);
            return $stats;
        }
        $this->_logMessage("Extracted " . count($latestFiles) . " files from latest zip.");

        // Extract previous zip only if a valid previous entry exists
        if ($previousEntry) {
            $previousTempDir = BASE_PATH . 'temp/website_diff_checker/diff_previous_' . $previousEntry['id'];
            if (!file_exists($previousTempDir)) {
                if (!@mkdir($previousTempDir, 0777, true)) {
                    if (!is_dir($previousTempDir)) {
                        $this->_logMessage("ERROR: Failed to create previous temp dir: {$previousTempDir}");
                        error_log("ERROR: Failed to create previous temp dir: {$previousTempDir}");
                        $this->_cleanupDirectory($latestTempDir, true); // Clean up latest too
                        return $stats;
                    }
                }
            }
            $this->_logMessage("Extracting previous zip file ('{$previousEntry['zip_file_path']}') to temp dir: {$previousTempDir}");
            $previousFiles = $this->_extractZipArchive($previousEntry['zip_file_path'], $previousTempDir);
            if ($previousFiles === false) {
                $this->_logMessage("ERROR: Failed to extract previous zip for entry ID {$previousEntry['id']}. Treating pages as 'new'.");
                error_log("ERROR: Failed to extract previous zip for entry ID {$previousEntry['id']} from {$previousEntry['zip_file_path']}");
                $this->_cleanupDirectory($previousTempDir, true); // Clean up failed extraction dir
                $previousEntry = null; // Cannot compare, treat as new
            } else {
                $this->_logMessage("Extracted " . count($previousFiles) . " files from previous zip.");
            }
        }


        // Get pages that are crawl_finished but not diff_finished for this entry
        $pagesToProcess = $this->dbManager->findByValues(
            ['website_diff_checker_id' => $entryId, 'is_crawl_finished' => 1, 'is_diff_finished' => 0],
            'website_diff_checker_pages'
        );

        if (empty($pagesToProcess)) {
            $this->_logMessage("No crawl-finished, non-diffed pages found for entry ID {$entryId}. Diff step complete.");
            if ($latestTempDir) $this->_cleanupDirectory($latestTempDir, true); if ($previousTempDir) $this->_cleanupDirectory($previousTempDir, true);
            return $stats; // Nothing to process
        }

        $this->_logMessage("Found " . count($pagesToProcess) . " pages to process for diff for entry ID {$entryId}.");

        $maxPagesPerBatch = 10; // Limit pages per execution
        $processedCount = 0;
        $this->_logMessage("Processing diff batch (max {$maxPagesPerBatch} pages)...");

        foreach ($pagesToProcess as $page) {
            if ($processedCount >= $maxPagesPerBatch) {
                $this->_logMessage("Reached max pages per diff batch ({$maxPagesPerBatch}). Stopping diff for now.");
                break;
            }

            $stats['pages_processed']++;
            $processedCount++;

            $pageUrl = $page['page_url'];
            $pageId = $page['id'];
            // *** Get the expected filename FROM THE DATABASE ***
            $expectedFilename = $page['saved_filename'] ?? null; // Use null coalescing

            $diffText = ""; $hasDiff = false; // hasDiff flag is used by compareHtmlFiles
            $statusMessage = ""; // Use this for status text if no diff is performed

            $this->_logMessage("  Processing page ID {$pageId}: {$pageUrl} (Expected file: " . ($expectedFilename ?: 'NONE_SAVED') . ")");

            // Check if filename was saved during crawl
            if (empty($expectedFilename)) {
                $this->_logMessage("  ERROR: Missing 'saved_filename' for page ID {$pageId}. Cannot perform diff.");
                error_log("ERROR: Missing 'saved_filename' for page ID {$pageId} (Entry {$entryId}, URL {$pageUrl}).");
                $stats['pages_failed']++;
                $diffText = "Error: Filename not saved during crawl step.";
                // Update DB immediately and continue to next page
                $pageUpdateData = ['is_diff_finished' => 1, 'diff_text' => $diffText, 'updated_at' => date('Y-m-d H:i:s')];
                $this->_updatePageStatus($pageId, $pageUpdateData);
                continue;
            }

            try {
                // --- Find the corresponding files using expectedFilename ---
                $latestFilePath = $latestTempDir ? ($latestFiles[$expectedFilename] ?? null) : null;
                $previousFilePath = $previousTempDir ? ($previousFiles[$expectedFilename] ?? null) : null;
                // --- End of finding files ---


                // --- Determine action based on file existence ---
                if ($latestFilePath && $previousEntry && $previousFilePath) {
                    // *** CONDITION MET: Both files exist, perform comparison ***
                    $this->_logMessage("  Comparing: {$previousFilePath} vs {$latestFilePath}");
                    $compareResult = $this->_compareHtmlFiles($previousFilePath, $latestFilePath);
                    $diffText = $compareResult['diff_text']; // Will contain diff or error message from comparison

                    if ($compareResult['has_diff']) {
                        if (strpos($diffText, "Error comparing files:") === 0 || strpos($diffText, "Error generating diff") === 0) {
                            $stats['pages_failed']++; $this->_logMessage("  Diff Error: {$diffText}");
                            error_log("Diff Error for page {$pageId} (Entry {$entryId}, File {$expectedFilename}): {$diffText}");
                        } else {
                            $stats['pages_with_changes']++; $this->_logMessage("  Changes detected.");
                        }
                    } else {
                        $stats['pages_without_changes']++; $this->_logMessage("  No changes detected.");
                    }

                } else {
                    // *** CONDITION NOT MET: Cannot compare, set status message ***
                    if ($latestFilePath && !$previousEntry) {
                        $statusMessage = "New page - no previous entry for comparison";
                        $stats['pages_new']++; $this->_logMessage("  Marked as new (no previous entry).");
                    } else if ($latestFilePath && $previousEntry && !$previousFilePath) {
                        $statusMessage = "New page - file '{$expectedFilename}' not found in previous crawl";
                        $stats['pages_new']++; $this->_logMessage("  Marked as new (file '{$expectedFilename}' not found in previous crawl).");
                    } else if (!$latestFilePath) {
                        $statusMessage = "Error: HTML file '{$expectedFilename}' not found in latest crawl zip.";
                        $stats['pages_failed']++; $this->_logMessage("  ERROR: File '{$expectedFilename}' not found in latest zip.");
                        error_log("ERROR: File '{$expectedFilename}' not found in latest zip for page {$pageId} (Entry {$entryId}).");
                    } else {
                        $statusMessage = "Error: Unknown file status for diff processing (File: {$expectedFilename}).";
                        $stats['pages_failed']++; $this->_logMessage("  ERROR: Unknown file state (File: {$expectedFilename}).");
                        error_log("ERROR: Unknown file state for diff processing page {$pageId} (Entry {$entryId}, File {$expectedFilename}).");
                    }
                    $diffText = $statusMessage; // Use the status message as the diff_text
                }
                // --- End of determining action ---

            } catch (Exception $e) {
                // Catch unexpected exceptions during file path logic etc.
                $stats['pages_failed']++;
                $diffText = "Exception during diff processing: " . substr($e->getMessage(), 0, 500); // Store exception as diff_text, limit length
                $this->_logMessage("  EXCEPTION processing diff: " . $e->getMessage());
                error_log("Exception processing diff for page {$pageUrl} (Entry {$entryId}, File {$expectedFilename}): " . $e->getMessage());
            }

            // Update page status in DB
            $pageUpdateData = [
                'is_diff_finished' => 1, // Mark as finished (processed or error)
                'diff_text' => $diffText, // Store the actual diff, status message, or error
                'updated_at' => date('Y-m-d H:i:s')
            ];
            if (!$this->_updatePageStatus($pageId, $pageUpdateData)) {
                $this->_logMessage("  ERROR: Failed to update DB status for page ID {$pageId}.");
                error_log("ERROR: Failed to update DB status for page ID {$pageId} after diff processing.");
                // If DB update fails, the page will be reprocessed next time, which might be okay
            }
        }

        // Clean up temporary directories AFTER the batch is processed
        if ($latestTempDir) $this->_cleanupDirectory($latestTempDir, true);
        if ($previousTempDir) $this->_cleanupDirectory($previousTempDir, true);
        $this->_logMessage("Cleaned up temporary directories for diff batch.");

        return $stats;
    }

    /**
     * Handles the AI page summarization step for a specific entry.
     * Assumes page diffing is finished.
     *
     * @param int $entryId The website_diff_checker entry ID.
     * @return array Statistics {'pages_processed', 'pages_succeeded', 'pages_failed', 'pages_skipped'}
     */
    private function _handlePageSummaryStep(int $entryId): array
    {
        // Use $this->dbManager
        $aiManager = new AiManager(); // Still assuming direct instantiation

        $stats = [
            'pages_processed' => 0,
            'pages_succeeded' => 0,
            'pages_failed' => 0,
            'pages_skipped' => 0
        ];

        // Get pages for this entry that are diff_finished but not summary_finished
        $pagesToProcess = $this->dbManager->findByValues(
            ['website_diff_checker_id' => $entryId, 'is_diff_finished' => 1, 'is_diff_summary_finished' => 0],
            'website_diff_checker_pages'
        );

        if (empty($pagesToProcess)) {
            return $stats; // Nothing to process
        }

        $maxPagesPerBatch = 3; // Limit AI calls
        $processedCount = 0;

        foreach ($pagesToProcess as $page) {
            if ($processedCount >= $maxPagesPerBatch) {
                break;
            }

            $stats['pages_processed']++;
            $processedCount++;

            $pageUrl = $page['page_url'];
            $pageId = $page['id'];
            $diffText = $page['diff_text'];

            // Skip pages with "No changes found" or "New page" diff text
            if ($diffText === "No changes found" || strpos($diffText, "New page") === 0 || empty(trim($diffText))) {
                $defaultSummary = "No changes were detected on this page.";
                if (strpos($diffText, "New page") === 0) { $defaultSummary = "This is a new page with no previous version to compare."; }
                else if (empty(trim($diffText))) { $defaultSummary = "Diff text was empty or only whitespace."; }

                $updateData = [
                    'is_diff_summary_finished' => 1,
                    'diff_summary_text' => $defaultSummary,
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                if ($this->_updatePageStatus($pageId, $updateData)) { $stats['pages_skipped']++; }
                else { $stats['pages_failed']++; }
                continue;
            }

            // Mark as finished BEFORE calling AI
            $updateData = [
                'is_diff_summary_finished' => 1,
                'diff_summary_text' => 'Generating summary...',
                'updated_at' => date('Y-m-d H:i:s')
            ];
            $this->_updatePageStatus($pageId, $updateData);

            try {
                $diffSummaryResult = $this->_createDiffSummary($diffText, $aiManager);
                $updateData = [
                    'diff_summary_text' => $diffSummaryResult['summary'],
                    'updated_at' => date('Y-m-d H:i:s')
                ];

                if ($this->_updatePageStatus($pageId, $updateData)) {
                    if ($diffSummaryResult['success']) { $stats['pages_succeeded']++; }
                    else { $stats['pages_failed']++; }
                } else {
                    $stats['pages_failed']++;
                }
            } catch (Exception $e) {
                $stats['pages_failed']++;
                $this->_logMessage("Exception during AI summary for page ID {$pageId} ({$pageUrl}): " . $e->getMessage());
                $updateData = [
                    'diff_summary_text' => 'Exception during summary generation: ' . $e->getMessage(),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                $this->_updatePageStatus($pageId, $updateData);
            }
        }

        return $stats;
    }

    /**
     * Creates the overall project summary report and updates the main entry.
     * Assumes all relevant page summaries are finished.
     *
     * @param int $entryId The website_diff_checker entry ID.
     * @return bool True on success, false on failure.
     */
    private function _handleProjectSummaryStep(int $entryId): bool
    {
        // Use $this->dbManager

        try {
            // Get the entry
            $entry = $this->dbManager->findById($entryId, 'website_diff_checker');
            if (empty($entry)) {
                $this->_logMessage("Entry not found with ID {$entryId} for project summary.");
                return false;
            }

            // Get all pages that were diff_finished
            $pagesWithDiffs = $this->dbManager->findByValues(
                ['website_diff_checker_id' => $entryId, 'is_diff_finished' => 1],
                'website_diff_checker_pages'
            );

            if (empty($pagesWithDiffs)) {
                $this->_logMessage("No diff-finished pages found for entry ID {$entryId}. Cannot create project summary.");
                $this->_updateMainEntryStatus($entryId, [
                    'diff_summary_text' => "Project summary failed: No pages were marked as 'diff_finished'.",
                    'is_diff_summary_finished' => 1,
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                return false;
            }

            // Create a summary report
            $diffReport = "Website Diff Report - " . date('Y-m-d H:i:s') . "\n\n";
            $diffReport .= "Entry ID: {$entryId}\n";
            $diffReport .= "Base URL: {$entry['website_url']}\n";
            $changedOrNewPages = [];
            foreach ($pagesWithDiffs as $page) {
                if ($page['diff_text'] !== "No changes found") {
                    $changedOrNewPages[] = $page;
                }
            }
            $diffReport .= "Total pages processed for diff: " . count($pagesWithDiffs) . "\n";
            $diffReport .= "Pages with changes or new: " . count($changedOrNewPages) . "\n\n";
            $reportDetails = "";
            if (!empty($changedOrNewPages)) {
                $reportDetails .= "--- Details for Changed or New Pages ---\n\n";
                foreach ($changedOrNewPages as $page) {
                    $pageUrl = $page['page_url']; $diffText = $page['diff_text']; $summaryText = $page['diff_summary_text'] ?? "No summary available.";
                    $reportDetails .= "Page: {$pageUrl}\n";
                    if (strpos($diffText, "New page") === 0) { $reportDetails .= "Status: New Page\nSummary: {$summaryText}\n\n"; }
                    elseif (strpos($diffText, "Error") === 0 || strpos($summaryText, "Error") === 0) { $reportDetails .= "Status: Error during processing\nError: " . ($summaryText !== "No summary available." ? $summaryText : $diffText) . "\n\n"; }
                    else { $reportDetails .= "Summary:\n{$summaryText}\n\n"; }
                }
            } else { $reportDetails .= "No changes detected on any pages.\n\n"; }
            $diffReport .= $reportDetails;

            // Update the entry
            $updateData = [
                'diff_summary_text' => $diffReport,
                'is_diff_summary_finished' => 1,
                'updated_at' => date('Y-m-d H:i:s')
            ];
            $success = $this->_updateMainEntryStatus($entryId, $updateData);

            if ($success) { return true; }
            else { $this->_logMessage("Failed to update entry with project diff summary for entry ID {$entryId}"); return false; }

        } catch (Exception $e) {
            $this->_logMessage("Error creating project diff summary for entry ID {$entryId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Updates specific fields for a page entry.
     *
     * @param int $pageId The page ID.
     * @param array $data The data to update.
     * @return bool True on success, false on failure.
     */
    private function _updatePageStatus(int $pageId, array $data): bool
    {
        // Use $this->dbManager
        try {
            $data['updated_at'] = date('Y-m-d H:i:s');
            $fields = array_keys($data);
            return $this->dbManager->updateFields($pageId, 'website_diff_checker_pages', $fields, $data);
        } catch (Exception $e) {
            $this->_logMessage("Error updating website_diff_checker_page ID {$pageId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Updates specific fields for the main entry.
     *
     * @param int $entryId The entry ID.
     * @param array $data The data to update.
     * @return bool True on success, false on failure.
     */
    private function _updateMainEntryStatus(int $entryId, array $data): bool
    {
        // Use $this->dbManager
        try {
            $data['updated_at'] = date('Y-m-d H:i:s'); // Corrected date format
            $fields = array_keys($data);
            return $this->dbManager->updateFields($entryId, 'website_diff_checker', $fields, $data);
        } catch (Exception $e) {
            $this->_logMessage("Error updating website_diff_checker entry ID {$entryId}: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Check if all pages for an entry are marked as crawl_finished.
     *
     * @param int $entryId
     * @return bool
     */
    private function _areAllPagesCrawled(int $entryId): bool
    {
        // Use $this->dbManager
        $remaining = $this->dbManager->findByValues(
            ['website_diff_checker_id' => $entryId, 'is_crawl_finished' => 0],
            'website_diff_checker_pages'
        );
        return empty($remaining);
    }

    /**
     * Check if all pages for an entry are marked as diff_finished.
     *
     * @param int $entryId
     * @return bool
     */
    private function _areAllPagesDiffFinished(int $entryId): bool
    {
        // Use $this->dbManager
        $remaining = $this->dbManager->findByValues(
            ['website_diff_checker_id' => $entryId, 'is_diff_finished' => 0],
            'website_diff_checker_pages'
        );
        return empty($remaining);
    }

    /**
     * Check if all pages that were marked as diff_finished are also marked as summary_finished.
     * This determines if the overall entry summary can be created.
     *
     * @param int $entryId
     * @return bool
     */
    private function _areAllRelevantPagesSummaryFinished(int $entryId): bool
    {
        // Use $this->dbManager
        // Find pages that were diffed but are not yet summary finished
        $remaining = $this->dbManager->findByValues(
            [
                'website_diff_checker_id' => $entryId,
                'is_diff_finished' => 1, // Only consider pages that were actually diffed
                'is_diff_summary_finished' => 0
            ],
            'website_diff_checker_pages'
        );
        return empty($remaining);
    }


    /**
     * Find internal links for a website URL using ScrapingAntCrawler.
     * Assumes ScrapingAntCrawler and ConfigManager are available.
     *
     * @param string $websiteUrl The website URL to crawl
     * @return array Array of internal links (URLs only, without anchor links)
     */
    private function _findInternalLinksByWebsiteUrl(string $websiteUrl): array
    {
        // Use ConfigManager::get() statically
        $scrapingAntApiToken = ConfigManager::get('scraping_ant.api_token');

        if (empty($scrapingAntApiToken)) {
            $this->_logMessage("ScrapingAnt API token not found in configuration for link finding.");
            return [];
        }

        try {
            $domain = parse_url($websiteUrl, PHP_URL_HOST);
            if (empty($domain)) {
                $this->_logMessage("Could not extract domain from URL: {$websiteUrl} for link finding.");
                return [];
            }

            $crawler = new ScrapingAntCrawler($scrapingAntApiToken, $websiteUrl, $domain);
            $crawler->fetch();

            if ($crawler->getStatusCode() != 200) {
                $this->_logMessage("Failed to fetch website for link finding: " . $websiteUrl . " (Status code: " . $crawler->getStatusCode() . ")");
                return [];
            }

            $internalLinks = $crawler->getInternalLinks();
            $processedLinks = [];
            // normalize root URL (no trailing slash)
            $websiteUrl = rtrim($websiteUrl, '/');
            $processedLinks[] = $websiteUrl;

            foreach ($internalLinks as $link) {
                $parts = explode(': ', $link, 2);
                if (count($parts) < 2) continue;
                $url = trim($parts[1]);
                if (strpos($url, '#') === 0) continue;
                $urlParts = explode('#', $url);
                // strip off anchor and normalize by removing trailing slash
                $cleanUrl = rtrim($urlParts[0], '/');

                if (!empty($cleanUrl) && !in_array($cleanUrl, $processedLinks)) {
                    $parsedCleanUrl = parse_url($cleanUrl);
                    if (isset($parsedCleanUrl['host']) && $parsedCleanUrl['host'] !== $domain) { continue; } // Skip external
                    $processedLinks[] = $cleanUrl;
                }
            }

            $processedLinks = array_unique($processedLinks);
            $processedLinks = array_values($processedLinks);

            return $processedLinks;
        } catch (Exception $e) {
            $this->_logMessage("Error finding internal links for {$websiteUrl}: " . $e->getMessage());
            return [];
        }
    }


    /**
     * Compare two HTML files and generate a diff.
     * Assumes diff command is available.
     *
     * @param string $previousFilePath
     * @param string $latestFilePath
     * @return array {'has_diff': bool, 'diff_text': string}
     */
    private function _compareHtmlFiles(string $previousFilePath, string $latestFilePath): array
    {
        $diffResult = ['has_diff' => false, 'diff_text' => "No changes found"];

        try {
            $latestContent = file_get_contents($latestFilePath);
            $previousContent = file_get_contents($previousFilePath);
            if ($latestContent === false || $previousContent === false) { throw new Exception("Failed to read file contents."); }

            $latestContentNormalized = $this->_normalizeHtml($latestContent);
            $previousContentNormalized = $this->_normalizeHtml($previousContent);

            if ($latestContentNormalized !== $previousContentNormalized) {
                $diffResult['has_diff'] = true;
                if ($this->_isDiffCommandAvailable()) {
                    $tempDir = BASE_PATH . 'temp/website_diff_checker/';
                    if (!file_exists($tempDir)) mkdir($tempDir, 0777, true);
                    $tempPrevFile = $tempDir . 'prev_' . uniqid() . '_' . basename($previousFilePath);
                    $tempLatestFile = $tempDir . 'latest_' . uniqid() . '_' . basename($latestFilePath);

                    if (file_put_contents($tempPrevFile, $previousContentNormalized) === false || file_put_contents($tempLatestFile, $latestContentNormalized) === false) {
                        if (file_exists($tempPrevFile)) unlink($tempPrevFile); if (file_exists($tempLatestFile)) unlink($tempLatestFile);
                        throw new Exception("Failed to write temporary files for diff.");
                    }

                    $diffCommand = "diff -uNa " . escapeshellarg($tempPrevFile) . " " . escapeshellarg($tempLatestFile) . " 2>&1";
                    $diffOutput = []; $diffReturnCode = 0;
                    exec($diffCommand, $diffOutput, $diffReturnCode);
                    if (file_exists($tempPrevFile)) unlink($tempPrevFile); if (file_exists($tempLatestFile)) unlink($tempLatestFile);

                    if ($diffReturnCode === 1) { $diffResult['diff_text'] = implode("\n", $diffOutput); }
                    else if ($diffReturnCode === 0) { $diffResult['has_diff'] = false; $diffResult['diff_text'] = "No changes found after normalization and diff command."; }
                    else { $errorMessage = "Error running diff command (code {$diffReturnCode}) for file: " . basename($latestFilePath) . ". Output: " . implode("\n", $diffOutput); $this->_logMessage($errorMessage); $diffResult['diff_text'] = "Error generating diff: " . $errorMessage; }
                } else { $diffResult['diff_text'] = "Diff command not available, but changes detected"; }
            }
        } catch (Exception $e) {
            $errorMessage = "Error comparing files: " . basename($previousFilePath) . " vs " . basename($latestFilePath) . " - " . $e->getMessage(); $this->_logMessage($errorMessage);
            $diffResult['has_diff'] = true; $diffResult['diff_text'] = "Error comparing files: " . $e->getMessage();
        }
        return $diffResult;
    }

    /**
     * Normalize HTML content for comparison
     *
     * @param string $html HTML content
     * @return string Normalized HTML
     */
    private function _normalizeHtml(string $html): string
    {
        if (empty($html)) { return ""; }
        $dom = new \DOMDocument();
        @$dom->loadHTML('<?xml encoding="utf-8"?>'.$html, LIBXML_NOERROR | LIBXML_NOWARNING);
        foreach (['script','style'] as $tag) {
            $nodes = $dom->getElementsByTagName($tag);
            for ($i = $nodes->length-1; $i>=0; $i--) { $node = $nodes->item($i); if ($node && $node->parentNode) { $node->parentNode->removeChild($node); } }
        }
        $xpath = new \DOMXPath($dom); $texts = [];
        foreach ($xpath->query('//text()[normalize-space()]') as $tn) { $text = $tn->textContent; if (!empty(trim($text))) { $texts[] = $text; } }
        $text = implode(' ', $texts);
        $text = html_entity_decode($text, ENT_QUOTES|ENT_HTML5, 'UTF-8');
        if (class_exists('Normalizer')) { $text = \Normalizer::normalize($text, \Normalizer::FORM_C); }
        $text = str_replace("\xc2\xa0", ' ', $text); $text = str_replace(['–','—'], '-', $text);
        $text = preg_replace('/\s+/', ' ', trim($text)); $text = preg_replace('/([\.!\?])\s+/', '$1'. "\n", $text);
        return trim($text)."\n";
    }

    /**
     * Check if the diff command is available on the system.
     *
     * @return bool True if diff is available, false otherwise
     */
    private function _isDiffCommandAvailable(): bool
    {
        static $isAvailable = null; if ($isAvailable !== null) { return $isAvailable; }
        $output = []; $returnCode = 0; exec('diff --version 2>&1', $output, $returnCode);
        $isAvailable = ($returnCode === 0); return $isAvailable;
    }

    /**
     * Create a diff summary for a given diff text using AI.
     *
     * @param string $diffText The diff text to summarize
     * @param AiManager $aiManager The AI manager instance
     * @return array The result containing success status and summary text
     */
    private function _createDiffSummary(string $diffText, AiManager $aiManager): array
    {
        $result = ['success' => false, 'summary' => ''];
        if (empty(trim($diffText))) { $result['summary'] = "Cannot summarize: Empty diff text provided."; return $result; }

        try {
            $prompt = "Analysiere den folgenden Unified-Diff und beschreibe die wesentlichen Änderungen knapp. Konzentriere dich auf inhaltliche Änderungen, die für Nutzer sichtbar sind (Text, Bilder, Sektionen), nicht auf kleine HTML-Struktur-, Attribut- oder technische Anpassungen (IDs, Klassen, Zeitstempel, Skripte, Styles). Ignoriere Änderungen, die nur Whitespace oder Zeilenumbrüche betreffen, sofern sie den sichtbaren Inhalt nicht signifikant ändern. Formatiere deine Antwort als prägnante Aufzählung der wichtigsten Änderungen, idealerweise in Stichpunkten.\n\nUNIFIED DIFF AUSGABE:\n" . $diffText;
            $maxPromptLength = 30000;
            if (strlen($prompt) > $maxPromptLength) {
                $diffPart = substr($diffText, 0, $maxPromptLength - (strlen($prompt) - strlen($diffText)) - 500);
                $prompt = "Analysiere die folgenden gekürzten Änderungen (basierend auf Unified-Diff) und beschreibe die wesentlichen Änderungen knapp. Konzentriere dich auf inhaltliche Änderungen, die für Nutzer sichtbar sind, nicht auf kleine HTML-Struktur- oder Attribut-Anpassungen. Ignoriere Änderungen an Zeitstempeln, IDs oder anderen technischen Details. Formatiere deine Antwort als Aufzählung der Änderungen.\n\nGEKÜRZTE ÄNDERUNGEN:\n" . $diffPart . "\n\n... (Inhalt gekürzt)";
            }
            $aiResponse = $aiManager->generateContent($prompt);
            if (empty(trim($aiResponse))) { $result['summary'] = "AI returned empty summary."; $result['success'] = true; }
            else { $result['success'] = true; $result['summary'] = $aiResponse; }
            return $result;
        } catch (Exception $e) {
            $errorMessage = "Error creating diff summary via AI: " . $e->getMessage(); $this->_logMessage($errorMessage);
            $result['summary'] = "Error analyzing changes with AI: " . $e->getMessage(); return $result;
        }
    }

    /**
     * Create a zip archive of HTML files
     *
     * @param int $entryId The entry ID
     * @param string $tempDir The temporary directory containing HTML files
     * @param int $projectId The project ID
     * @return string|false The path to the zip file or false on failure
     */
    private function _createZipArchive(int $entryId, string $tempDir, int $projectId)
    {
        $projectFilesDir = BASE_PATH . 'project_files/' . $projectId;
        if (!file_exists($projectFilesDir)) { if (!mkdir($projectFilesDir, 0777, true)) { $this->_logMessage("Failed to create project files directory: {$projectFilesDir}"); return false; } }
        $zipFileName = 'website_diff_checker_' . $entryId . '_' . date('Y-m-d_H-i-s') . '.zip';
        $zipFilePath = $projectFilesDir . '/' . $zipFileName;
        $zip = new ZipArchive();
        if ($zip->open($zipFilePath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) { $this->_logMessage("Failed to create zip archive: {$zipFilePath}"); return false; }
        $htmlFiles = glob($tempDir . '/*.html');
        if ($htmlFiles === false) { $this->_logMessage("Error listing files in temp directory: {$tempDir}"); $zip->close(); if (file_exists($zipFilePath)) unlink($zipFilePath); return false; }
        if (empty($htmlFiles)) { $this->_logMessage("No HTML files found in {$tempDir} to zip."); $zip->close(); return false; }
        $fileCount = 0;
        foreach ($htmlFiles as $htmlFile) { $fileName = basename($htmlFile); if ($zip->addFile($htmlFile, $fileName)) { $fileCount++; } else { $this->_logMessage("Failed to add file to zip: {$htmlFile}"); } }
        $zip->close();
        if ($fileCount === 0) { $this->_logMessage("Zero files added to zip archive: {$zipFilePath}. Deleting empty zip."); if (file_exists($zipFilePath)) unlink($zipFilePath); return false; }
        return $zipFilePath;
    }

    /**
     * Extract files from a zip archive
     *
     * @param string $zipFilePath Path to the zip file
     * @param string $extractDir Directory to extract to
     * @return array|false Associative array of extracted files (filename => path) or false on failure
     */
    private function _extractZipArchive(string $zipFilePath, string $extractDir)
    {
        if (!file_exists($zipFilePath)) { $this->_logMessage("Zip file not found for extraction: {$zipFilePath}"); return false; }
        if (!is_dir($extractDir)) { if (!mkdir($extractDir, 0777, true)) { $this->_logMessage("Failed to create extraction directory: {$extractDir}"); return false; } }
        $zip = new ZipArchive();
        if ($zip->open($zipFilePath) !== true) { $this->_logMessage("Failed to open zip file: {$zipFilePath}"); return false; }
        if (!$zip->extractTo($extractDir)) { $this->_logMessage("Failed to extract zip file to {$extractDir}"); $zip->close(); $this->_cleanupDirectory($extractDir, true); return false; }
        $extractedFiles = [];
        for ($i = 0; $i < $zip->numFiles; $i++) { $fileName = $zip->getNameIndex($i); if (substr($fileName, -1) !== '/' && $fileName !== '.' && $fileName !== '..') { $extractedFiles[$fileName] = $extractDir . '/' . $fileName; } }
        $zip->close();
        return $extractedFiles;
    }

    /**
     * Clean up a directory by removing all files and optionally the directory itself
     *
     * @param string $dir Directory to clean up
     * @param bool $removeDir Whether to remove the directory itself
     * @return void
     */
    private function _cleanupDirectory(string $dir, bool $removeDir = false): void
    {
        if (!is_dir($dir)) { return; }
        $items = array_diff(scandir($dir) ?: [], ['.', '..']);
        foreach ($items as $item) { $itemPath = $dir . DIRECTORY_SEPARATOR . $item; if (is_file($itemPath) || is_link($itemPath)) { if (!unlink($itemPath)) { $this->_logMessage("Failed to delete file/link during cleanup: {$itemPath}"); } } elseif (is_dir($itemPath)) { $this->_cleanupDirectory($itemPath, true); } }
        if ($removeDir && is_dir($dir)) { if (count(array_diff(scandir($dir) ?: [], ['.', '..'])) === 0) { if (!rmdir($dir)) { $this->_logMessage("Failed to delete directory during cleanup: {$dir}"); } } else { $this->_logMessage("Directory {$dir} is not empty after cleanup, cannot remove."); } }
    }

    /**
     * Get the last crawl entry for a specific project
     *
     * @param int $projectId The project ID
     * @return array|null The last crawl entry or null if not found
     */
    private function getLastCrawlByProjectId(int $projectId): ?array
    {
        try {
            $entries = $this->dbManager->findByValues(
                ['project_id' => $projectId],
                'website_diff_checker',
                'created_at DESC',
                1
            );

            return !empty($entries) ? $entries[0] : null;
        } catch (Exception $e) {
            $this->_logMessage("Error getting last crawl for project {$projectId}: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Log a message to the configured log file.
     *
     * @param string $message The message to log
     * @return void
     */
    private function _logMessage(string $message): void
    {
        // Check if logging is available (log file path was set)
        if ($this->logFilePath === null) {
            return; // Logging disabled or failed to initialize
        }

        $timestamp = date('Y-m-d H:i:s');
        // Add Process ID to logs for concurrent runs
        $pid = getmypid();
        $logMessage = "[{$timestamp}][PID:{$pid}] {$message}" . PHP_EOL;

        // Use file_put_contents and check result
        if (@file_put_contents($this->logFilePath, $logMessage, FILE_APPEND | LOCK_EX) === false) {
            // Fallback to $this->_logMessage if writing to the log file fails
            $this->_logMessage("WEBSITE_DIFF_LOG_WRITE_ERROR: Failed to write to {$this->logFilePath} - Message: {$logMessage}");
        }
    }
}