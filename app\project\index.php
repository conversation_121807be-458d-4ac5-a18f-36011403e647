<?php

include  '../_base/base.php';
require_once '../../classes/wordpress_manager.php';
require_once '../../classes/project_manager.php';
require_once '../../classes/rich_snippet_manager.php';

$project    = $dbManager->findById( $_GET['id'], 'projects');
$client     = $dbManager->findById( $project['client_id'] , 'clients');

// Get competitor projects
$projectManager = new ProjectManager();
$competitorProjects = $projectManager->getCompetitorProjects($project['id']);

include APP_PATH . 'layouts/head.php';


?>

<body>

<div class="page">

    <div class="page-wrapper">

        <?php include APP_PATH . 'layouts/nav.php';  ?>

        <div class="page-header d-print-none">
            <div class="container-xl">
                <div class="row g-2 align-items-center">
                    <div class="col">
                        <a href="/home/" class="btn btn-icon btn-outline-secondary float-start me-3">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-chevron-left">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                                <path d="M15 6l-6 6l6 6"></path>
                            </svg>
                        </a>
                        <!-- Page pre-title -->
                        <div class="page-pretitle">Projekt</div>
                        <h2 class="page-title"><?php echo $project['name'] ?></h2>
                    </div>
                    <!-- Page title actions -->
                    <div class="col-auto ms-auto d-print-none">
                        <div class="btn-list">

                            <form action="" method="post" class="d-inline">
                                <input type="hidden" name="table" value="projects">
                                <input type="hidden" name="id" value="<?php echo $project['id']; ?>">
                                <input type="hidden" name="overlay_template" value="project/add_competitor_project_overlay">
                                <input type="hidden" name="action" value="overlay">
                                <input type="hidden" name="show_loading" value="1">
                                <button type="submit" class="btn btn-outline-primary ">
                                    <svg  xmlns="http://www.w3.org/2000/svg"  width="24"  height="24"  viewBox="0 0 24 24"  fill="none"  stroke="currentColor"  stroke-width="2"  stroke-linecap="round"  stroke-linejoin="round"  class="icon icon-tabler icons-tabler-outline icon-tabler-plus"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M12 5l0 14" /><path d="M5 12l14 0" /></svg>
                                    Wettbewerber anlegen
                                </button>
                            </form>

                            <form action="" method="post">
                                <input type="hidden" name="table" value="projects">
                                <input type="hidden" name="id" value="<?php echo $project['id']; ?>">
                                <input type="hidden" name="overlay_template" value="project/website_options_overlay">
                                <input type="hidden" name="action" value="overlay">
                                <input type="hidden" name="show_loading" value="1">

                                <button type="submit" class="btn btn-outline-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-world">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0" />
                                        <path d="M3.6 9h16.8" />
                                        <path d="M3.6 15h16.8" />
                                        <path d="M11.5 3a17 17 0 0 0 0 18" />
                                        <path d="M12.5 3a17 17 0 0 1 0 18" />
                                    </svg>
                                    Website Einstellungen
                                </button>
                            </form>

                            <form action="" method="post">
                                <input type="hidden" name="table" value="projects">
                                <input type="hidden" name="id" value="<?php echo $project['id']; ?>">
                                <input type="hidden" name="overlay_template" value="project/project_options_overlay">
                                <input type="hidden" name="action" value="overlay">
                                <input type="hidden" name="show_loading" value="1">

                                <button type="submit" class="btn btn-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-settings">
                                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                        <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z" />
                                        <path d="M12 12m-3 0a3 3 0 1 0 6 0a3 3 0 1 0 -6 0" />
                                    </svg>
                                    Projekt-Einstellungen
                                </button>
                            </form>




                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="page-body">
            <div class="container-xl">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Projekt Informationen</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Projektname</label>
                                            <div class="form-control-plaintext"><?php echo htmlspecialchars($project['name']); ?></div>
                                        </div>
                                    </div>
                                    <?php if (!empty($client['name'])): ?>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Kunde</label>
                                            <div class="form-control-plaintext"><?php echo htmlspecialchars($client['name']); ?></div>
                                        </div>
                                    </div>
                                    <?php endif; ?>
                                </div>

                                <?php if (!empty($project['info_data'])): ?>
                                <div class="hr-text">Unternehmensdaten</div>
                                <div class="row">
                                    <?php
                                    // Unternehmensdaten anzeigen
                                    $companyData = $project['info_data'];

                                    // Definieren der anzuzeigenden Felder mit Labels
                                    $fields = [
                                        'name' => 'Name',
                                        'company' => 'Firma',
                                        'contact_name' => 'Ansprechpartner',
                                        'phone' => 'Telefon',
                                        'email' => 'E-Mail',
                                        'street' => 'Straße',
                                        'zip' => 'PLZ',
                                        'city' => 'Ort',
                                        'about' => 'Über uns'
                                    ];

                                    // Felder anzeigen
                                    foreach ($fields as $field => $label):
                                        if (!empty($companyData[$field])):
                                    ?>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label text-primary mb-0"><?php echo htmlspecialchars($label); ?></label>
                                            <div class="form-control-plaintext p-0"><?php echo htmlspecialchars($companyData[$field]); ?></div>
                                        </div>
                                    </div>
                                    <?php
                                        endif;
                                    endforeach;
                                    ?>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Wettbewerber Projekte -->
                    <?php if (!empty($competitorProjects)): ?>
                    <div class="col-md-12 mt-3">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Wettbewerber</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($competitorProjects as $competitorProject): ?>
                                        <?php
                                            // Include the project layout file for each competitor project
                                            $project = $competitorProject;
                                            include APP_PATH . 'project/project.php';
                                            // Restore the original project variable
                                            $project = $dbManager->findById($_GET['id'], 'projects');
                                        ?>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Funktionen Tabelle -->
                    <div class="col-md-12 mt-3">
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">Funktionen</h3>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-vcenter card-table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Status</th>
                                                <th>Name</th>
                                                <th>Datenbank</th>
                                                <th>Beschreibung</th>
                                                <th>Info</th>
                                                <th class="w-1">Einstellungen</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php include APP_PATH . 'project/info_data/row_project_context.php'; ?>
                                            <?php include APP_PATH . 'project/wordpress_image_handler/row_wordpress_image_handling.php'; ?>
                                            <?php include APP_PATH . 'project/website_diff_checker/row_website_diff_checker.php'; ?>
                                            <?php include APP_PATH . 'project/page_speed/row_page_speed.php'; ?>
                                            <?php include APP_PATH . 'project/rich_snippets/table_row.php'; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Rich Snippets Tabelle -->
                    <div class="col-md-12 mt-3">
                        <?php include APP_PATH . 'project/rich_snippets/table.php'; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php
include APP_PATH . 'layouts/footer.php';
include APP_PATH . 'layouts/scripts.php';
?>

</body>
</html>
