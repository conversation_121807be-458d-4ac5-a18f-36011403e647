<?php


// Führe Leads-Prozesse immer aus
include_once BASE_PATH . 'process/leads/index.php';

// Überprü<PERSON>, ob die aktuelle Zeit zwischen 2 Uhr und 4 Uhr nachts liegt
if (isWithinTimeWindow(2, 4)) {
    // Diese Prozesse werden nur zwischen 2 Uhr und 4 Uhr nachts ausgeführt
    echo "Ausführung der nächtlichen Prozesse (2-4 Uhr)...\n";
    // WordPress-Bildverarbeitung
    include_once BASE_PATH . 'process/wordpress_handle_images.php';

} else {
    echo "WordPress-Bildverarbeitung wird nur zwischen 2 Uhr und 4 Uhr nachts ausgeführt.\n";
}

$websiteDiffChecker = new WebsiteDiffChecker();
$websiteDiffChecker->handle();

$websiteDiffChecker = new PageSpeedManager();
$websiteDiffChecker->handleEntries();

$dataForSeoManager = new DataForSeoManager();
$dataForSeoManager->processPendingTasks();

include_once BASE_PATH . 'process/update_confirmed_images.php';

// Funktion zur Überprüfung, ob die aktuelle Zeit innerhalb eines bestimmten Zeitfensters liegt
function isWithinTimeWindow($startHour, $endHour) {
    $currentHour = (int)date('G'); // 24-Stunden-Format ohne führende Nullen (0-23)

    // Wenn das Zeitfenster über Mitternacht geht (z.B. 22-2)
    if ($startHour > $endHour) {
        return ($currentHour >= $startHour || $currentHour < $endHour);
    }

    // Normales Zeitfenster (z.B. 2-4)
    return ($currentHour >= $startHour && $currentHour < $endHour);
}
