<?php
// Rich Snippets Table Row for the functions table in project/index.php
// This template displays a row in the functions table for rich snippets

// Get project ID
$projectId = $project['id'] ?? 0;

// Initialize RichSnippetManager
$richSnippetManager = new RichSnippetManager();

// Get rich snippets for this project
$richSnippets = $richSnippetManager->getRichSnippetsByProjectId($projectId);

// Count statistics
$totalSnippets = count($richSnippets);
$generatedSnippets = 0;
$publishedSnippets = 0;
$hasGlobalSnippet = false;

foreach ($richSnippets as $snippet) {
    if ($snippet['is_generated']) {
        $generatedSnippets++;
    }
    if ($snippet['is_published']) {
        $publishedSnippets++;
    }
    if ($snippet['is_global_snippet'] && $snippet['is_generated']) {
        $hasGlobalSnippet = true;
    }
}

// Determine status
$status = 'Nicht konfiguriert';
$statusClass = 'danger';

if ($totalSnippets > 0) {
    if ($publishedSnippets === $totalSnippets) {
        $status = 'Vollständig';
        $statusClass = 'success';
    } elseif ($generatedSnippets > 0) {
        $status = 'Teilweise generiert';
        $statusClass = 'warning';
    } else {
        $status = 'Nicht generiert';
        $statusClass = 'danger';
    }
}

// Determine database info
$dbInfo = "Tabelle: rich_snippets<br>Einträge: {$totalSnippets}";
?>

<tr>
    <td>
        <span class="badge text-white bg-<?= $statusClass ?>"><?= $status ?></span>
    </td>
    <td>Rich Snippets</td>
    <td><?= $dbInfo ?></td>
    <td>Strukturierte Daten für Suchmaschinen</td>
    <td>
        <?php if ($totalSnippets > 0): ?>
            <span class="text-muted">
                <?= $generatedSnippets ?>/<?= $totalSnippets ?> generiert<br>
                <?= $publishedSnippets ?>/<?= $totalSnippets ?> veröffentlicht
            </span>
        <?php else: ?>
            <span class="text-muted">Keine Rich Snippets gefunden</span>
        <?php endif; ?>
    </td>
    <td>
        <form action="" method="post">
            <input type="hidden" name="table" value="projects">
            <input type="hidden" name="field" value="rich_snippet_options_data">
            <input type="hidden" name="id" value="<?= $projectId ?>">
            <input type="hidden" name="overlay_template" value="/project/rich_snippets/settings_overlay">
            <input type="hidden" name="is_hard_modal" value="false">
            <input type="hidden" name="action" value="overlay">

            <button type="submit" class="btn btn-sm btn-outline-primary">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-mail">
                    <path stroke="none" d="M0 0h24v24H0z" fill="none"></path>
                    <path d="M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z"></path>
                    <path d="M3 7l9 6l9 -6"></path>
                </svg>
                Einstellungen
            </button>
        </form>
    </td>
</tr>
