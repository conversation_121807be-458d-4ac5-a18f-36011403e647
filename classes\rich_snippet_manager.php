<?php

/**
 * Rich Snippet Manager Class
 *
 * A class that manages rich snippets for WordPress pages, including scanning pages,
 * generating rich snippet data, and publishing to WordPress.
 */
class RichSnippetManager
{
    /**
     * Database manager instance
     *
     * @var DbManager
     */
    private DbManager $dbManager;

    /**
     * Constructor
     *
     * @param DbManager $dbManager The database manager instance
     */
    public function __construct($dbManager = null)
    {
        global $dbManager;
        $this->dbManager = $dbManager;
    }

    /**
     * Scan all published pages from a WordPress site by project ID
     * and add them to the rich_snippets table
     *
     * @param int $projectId The ID of the project
     * @return array Response array with success status and data
     */
    public function scanPagesByProjectId(int $projectId): array
    {
        $response = ['success' => 0];

        try {
            // Get project data
            $project = $this->dbManager->findById($projectId, 'projects');
            if (empty($project)) {
                $response['error'] = "Projekt mit ID {$projectId} nicht gefunden.";
                return $response;
            }

            // Get WordPress settings from options_data
            $websiteOptions = $project['website_options_data'] ?? [];

            // Check if WordPress credentials are set
            if (empty($websiteOptions['wp_username']) || empty($websiteOptions['wp_password'])) {
                $response['error'] = "WordPress Zugangsdaten sind nicht konfiguriert.";
                return $response;
            }

            // Check for required WordPress settings
            // Use the project's website_url field if wp_site_url is not set
            $wpSiteUrl = $project['website_url'];

            if (empty($wpSiteUrl)) {
                $response['error'] = "Website URL ist nicht konfiguriert.";
                return $response;
            }

            // Create WordPress Manager instance
            $wpManager = new WordpressManager(
                $wpSiteUrl,
                'wp/v2',
                $websiteOptions['wp_username'] ?? null,
                $websiteOptions['wp_password'] ?? null,
                true
            );

            // Get WordPress site settings to find the home page ID
            $wpSettings = $wpManager->getSettings();
            $homePageId = $wpSettings['page_on_front'] ?? null;

            // Get all published pages
            $pages = $wpManager->getAllPublishedPages();
            if ($pages === null) {
                $response['error'] = "Fehler beim Abrufen der Seiten von der WordPress-Website.";
                return $response;
            }

            // Count of pages added to the database
            $addedCount = 0;
            $skippedCount = 0;
            $globalSnippetAdded = false;
            $homePageFound = false;

            // Process each page
            foreach ($pages as $page) {
                // Check if the page already exists in the rich_snippets table
                $results = $this->dbManager->findByValues([
                    'project_id' => $projectId,
                    'wordpress_page_id' => $page['id']
                ], 'rich_snippets');

                $existingPage = $results[0] ?? null;

                // If the page exists, check if it's the homepage and update if needed
                if (!empty($existingPage)) {

                    // Check if this is the homepage
                    $isHomePage = ($homePageId && $page['id'] == $homePageId);
//
                    // Update the homepage flag if needed
                    if ($isHomePage && $existingPage['is_homepage'] != 1) {
                        $this->dbManager->updateFields(
                            $existingPage['id'],
                            'rich_snippets',
                            ['is_homepage'],
                            ['is_homepage' => 1]
                        );
                        $homePageFound = true;
                    }
//
                    $skippedCount++;
                    continue;
                }

                // Prepare data for insertion
                $pageData = [
                    'project_id' => $projectId,
                    'wordpress_page_id' => $page['id'],
                    'url' => $page['link'],
                    'is_generated' => 0,
                    'is_published' => 0,
                    'is_global_snippet' => 0,
                    'is_homepage' => 0
                ];

                // Check if this is the homepage
                if ($homePageId && $page['id'] == $homePageId) {
                    $pageData['is_homepage'] = 1;
                    $homePageFound = true;
                }

                // Insert the page into the rich_snippets table
                $this->dbManager->insertNewRowByData('rich_snippets', $pageData);
                $addedCount++;
            }

            // Add a global snippet entry if it doesn't exist yet
            $existingGlobalSnippet = $this->dbManager->findByValues([
                'project_id' => $projectId,
                'is_global_snippet' => 1
            ], 'rich_snippets');

            if (empty($existingGlobalSnippet)) {
                $globalSnippetData = [
                    'project_id' => $projectId,
                    'wordpress_page_id' => 0, // Use 0 to indicate it's not tied to a specific page
                    'url' => $wpSiteUrl,
                    'is_generated' => 0,
                    'is_published' => 0,
                    'is_global_snippet' => 1,
                    'is_homepage' => 0
                ];

                $this->dbManager->insertNewRowByData('rich_snippets', $globalSnippetData);
                $globalSnippetAdded = true;
                $addedCount++;
            }

            // Prepare success response
            $response['success'] = 1;
            $response['data'] = [
                'total_pages' => count($pages),
                'added_pages' => $addedCount,
                'skipped_pages' => $skippedCount,
                'global_snippet_added' => $globalSnippetAdded,
                'homepage_found' => $homePageFound
            ];

        } catch (Exception $e) {
            $response['error'] = "Fehler beim Scannen der WordPress-Seiten: " . $e->getMessage();
        }

        return $response;
    }

    /**
     * Generate rich snippet data for a specific rich snippet entry
     *
     * @param int $richSnippetId The ID of the rich snippet entry
     * @return array Response array with success status and data
     */
    public function generateByRichSnippetId(int $richSnippetId): array
    {
        $response = ['success' => 0];

        try {
            // Get rich snippet entry
            $richSnippet = $this->dbManager->findById($richSnippetId, 'rich_snippets');
            if (empty($richSnippet)) {
                $response['error'] = "Rich Snippet mit ID {$richSnippetId} nicht gefunden.";
                return $response;
            }

            // Get project data
            $projectId = $richSnippet['project_id'];
            $project = $this->dbManager->findById($projectId, 'projects');
            if (empty($project)) {
                $response['error'] = "Projekt mit ID {$projectId} nicht gefunden.";
                return $response;
            }

            // Check if this is the global snippet
            $isGlobalSnippet = ($richSnippet['is_global_snippet'] == 1);

            // If not generating the global snippet, check if global snippet exists and has content
            if (!$isGlobalSnippet) {
                // Find the global snippet for this project
                $globalSnippet = $this->dbManager->findByValues([
                    'project_id' => $projectId,
                    'is_global_snippet' => 1,
                    'is_generated' => 1
                ], 'rich_snippets')[0];

                // If global snippet doesn't exist or has no content, abort
                if (empty($globalSnippet) || empty($globalSnippet['rich_snippet_result'])) {
                    $response['error'] = "Globales Rich Snippet für dieses Projekt wurde noch nicht generiert. Bitte zuerst das globale Rich Snippet generieren.";
                    return $response;
                }
            }

            //print_r( $globalSnippet );
            //exit;

            // Create AI Manager instance
            $aiManager = new AiManager();

            // Create Website Manager instance
            $websiteManager = new WebsiteManager($aiManager);

            // Analyze the website to get the full text
            $success = $websiteManager->analyzeWebsite($richSnippet['url']);
            if (!$success) {
                $response['error'] = "Fehler beim Abrufen der Webseite: {$richSnippet['url']}";
                return $response;
            }

            // Get the full text from the website
            $fullText = $websiteManager->getFullText();
            if (empty($fullText)) {
                $response['error'] = "Kein Text auf der Webseite gefunden.";
                return $response;
            }

            // Get page title and meta description for additional context
            $pageTitle = $websiteManager->getWebsiteCrawler()->getTitle();
            $metaDescription = $websiteManager->getWebsiteCrawler()->getMetaDescription();

            // Prepare context for AI
            $context = [
                'url' => $richSnippet['url'],
                'title' => $pageTitle,
                'meta_description' => $metaDescription,
                'context' => $project['info_data'],
                'rich snippet context' => $project['rich_snippet_options_data']['context'] ?? ''
            ];

            // If this is not the global snippet, add the global snippet content to the context
            if (!$isGlobalSnippet && !empty($globalSnippet['rich_snippet_result'])) {
                $context['global_rich_snippet'] = $globalSnippet['rich_snippet_result'];
            }

            // If this is the global snippet, add a flag to indicate that
            if ($isGlobalSnippet) {
                $context['is_global_snippet'] = true;
            }

            // Create system prompt for generating LD+JSON
            if ($isGlobalSnippet) {
                $systemPrompt = 'Erstelle ein globales LD+JSON Snippet für die Website. ' .
                    'Dieses Snippet soll allgemeine Informationen über die Organisation/Firma enthalten. ' .
                    'Verwende Schema.org Organization oder LocalBusiness als Haupttyp. ' .
                    'Füge Kontaktdaten, Logo, Social Media Profile und andere relevante Informationen hinzu. '.
                    'Kein aggregateRating hinzufügen! @id hinzufügen! latitude/longitude kürzen auf 6 Nachkommastellen! logo url hinzufügen! image nur hinzufügen wenn image url vorhanden! ' .
                    'Gib nur ein umfassendes LD+JSON Snippet zurück, ohne script Tag, ohne zusätzliche Erklärungen. ' .
                    'Verwende die folgenden Kontextinformationen: ' . json_encode($context, JSON_UNESCAPED_UNICODE);
            } else {
                $systemPrompt = 'Erstelle ein spezifisches LD+JSON Snippet für diese einzelne Webseite. ' .
                    'Analysiere den Inhalt und erstelle passende strukturierte Daten: "@type": "WebPage"' .
                    'Berücksichtige den Seitentyp und den Inhalt. ' .
                    'Verwende das globale Rich Snippet als Referenz für Organisationsdaten, verlinke es und verwende für die mainEntity nur @id. Kein aggregateRating hinzufügen! Keine GeoDaten hinzufügen! Keine SearchAction hinzufügen! ' .
                    'Gib nur ein umfassendes LD+JSON Snippet zurück, ohne script Tag, ohne zusätzliche Erklärungen. ' .
                    'Verwende die folgenden Kontextinformationen: ' . json_encode($context, JSON_UNESCAPED_UNICODE);
            }

            // Generate LD+JSON snippets using AI
            $aiManager->setDefaultProvider('openai');
            $aiManager->setModel('gpt-4o'); // Use a capable model for structured data
            $ldJsonSnippets = $aiManager->generateContent($fullText, $systemPrompt);
            //$aiManager->

            //print_r($systemPrompt);
//
            //echo '<br><br>';

            //print_r($ldJsonSnippets);

            if( ! $ldJsonSnippets ) {
                $response['error'] = "Kein Rich Snippet generiert";
                return $response;
            }



            // Clean up the response to ensure it's valid JSON
            //$ldJsonSnippets = trim($ldJsonSnippets);
            //$ldJsonSnippets = preg_replace('/^```json\s*|\s*```$/s', '', $ldJsonSnippets);

            // Validate JSON
            //json_decode($ldJsonSnippets);
            //if (json_last_error() !== JSON_ERROR_NONE) {
            //    // If not valid JSON, try to fix common issues
            //    $ldJsonSnippets = preg_replace('/^<script[^>]*>|<\/script>$/s', '', $ldJsonSnippets);
//
            //    // Check again
            //    json_decode($ldJsonSnippets);
            //    if (json_last_error() !== JSON_ERROR_NONE) {
            //        $response['error'] = "Generierter LD+JSON Code ist nicht valide: " . json_last_error_msg();
            //        return $response;
            //    }
            //}

            // Update the rich snippet entry with the generated data
            $updateData = [
                'rich_snippet_result' => $ldJsonSnippets,
                'is_generated' => 1
            ];

            $this->dbManager->updateFields($richSnippetId, 'rich_snippets', array_keys($updateData), $updateData);

            // Prepare success response
            $response['success'] = 1;
            $response['data'] = [
                'rich_snippet_id' => $richSnippetId,
                'url' => $richSnippet['url'],
                'snippet_preview' => $ldJsonSnippets
            ];

        } catch (Exception $e) {
            $response['error'] = "Fehler bei der Generierung des Rich Snippets: " . $e->getMessage();
        }

        return $response;
    }

    /**
     * Publish rich snippet data to WordPress
     *
     * @param int $richSnippetId The ID of the rich snippet entry
     * @return array Response array with success status and data
     */
    public function publishByRichSnippetId(int $richSnippetId): array
    {
        $response = ['success' => 0];

        try {
            // Get rich snippet entry
            $richSnippet = $this->dbManager->findById($richSnippetId, 'rich_snippets');
            if (empty($richSnippet)) {
                $response['error'] = "Rich Snippet mit ID {$richSnippetId} nicht gefunden.";
                return $response;
            }

            // Check if the snippet has been generated
            if ($richSnippet['is_generated'] != 1 || empty($richSnippet['rich_snippet_result'])) {
                $response['error'] = "Rich Snippet wurde noch nicht generiert. Bitte zuerst generieren.";
                return $response;
            }

            // Check if already published
            //if ($richSnippet['is_published'] == 1) {
            //    $response['error'] = "Rich Snippet wurde bereits veröffentlicht.";
            //    return $response;
            //}

            // Get project data
            $projectId = $richSnippet['project_id'];
            $project = $this->dbManager->findById($projectId, 'projects');
            if (empty($project)) {
                $response['error'] = "Projekt mit ID {$projectId} nicht gefunden.";
                return $response;
            }

            // Get WordPress settings from options_data
            $websiteOptions = $project['website_options_data'] ?? [];

            // Check if WordPress credentials are set
            if (empty($websiteOptions['wp_username']) || empty($websiteOptions['wp_password'])) {
                $response['error'] = "WordPress Zugangsdaten sind nicht konfiguriert.";
                return $response;
            }

            // Check for required WordPress settings
            // Use the project's website_url field if wp_site_url is not set
            $wpSiteUrl = $project['website_url'];

            if (empty($wpSiteUrl)) {
                $response['error'] = "Website URL ist nicht konfiguriert.";
                return $response;
            }

            // Create WordPress Manager instance
            $wpManager = new WordpressManager(
                $wpSiteUrl,
                'wp/v2',
                $websiteOptions['wp_username'] ?? null,
                $websiteOptions['wp_password'] ?? null,
                true
            );

            $res = $wpManager->getPageMeta($richSnippet['wordpress_page_id']);

            //print_r($res);
            //exit;

            // Prepare metadata for WordPress
            $meta = [
                'rich_snippet' => $richSnippet['rich_snippet_result']
            ];

            //Wenn Homepage, globales Snippet setzen:
            if( $richSnippet['is_homepage'] ) {

                $globalRichSnippet = $this->findGlobalRichSnippetByProjectId( $richSnippet['project_id'] );
                $meta['rich_snippet_global'] = $globalRichSnippet['rich_snippet_result'];
            }

            //print_r($meta);
            //exit;

            // Update the page metadata in WordPress
            $success = $wpManager->updatePageMeta($richSnippet['wordpress_page_id'], $meta);

            if (!$success) {
                $response['error'] = "Fehler beim Veröffentlichen des Rich Snippets in WordPress.";
                return $response;
            }

            // Update the rich snippet entry to mark it as published
            $updateData = [
                'is_published' => 1
            ];

            $this->dbManager->updateFields($richSnippetId, 'rich_snippets', array_keys($updateData), $updateData);

            // Prepare success response
            $response['success'] = 1;
            $response['data'] = [
                'rich_snippet_id' => $richSnippetId,
                'wordpress_page_id' => $richSnippet['wordpress_page_id'],
                'url' => $richSnippet['url']
            ];

        } catch (Exception $e) {
            $response['error'] = "Fehler bei der Veröffentlichung des Rich Snippets: " . $e->getMessage();
        }

        return $response;
    }

    /**
     * Get all rich snippets for a specific project
     *
     * @param int $projectId The ID of the project
     * @return array Array of rich snippets or empty array if none found
     */
    public function getRichSnippetsByProjectId(int $projectId): array
    {
        // Find all rich snippets for the project
        $richSnippets = $this->dbManager->findByValues([
            'project_id' => $projectId
        ], 'rich_snippets');

        // Return the result or an empty array if no results
        return $richSnippets ?: [];
    }

    /**
     * Find the global rich snippet for a specific project
     *
     * @param int $projectId The ID of the project
     * @return array|null The global rich snippet or null if not found
     */
    public function findGlobalRichSnippetByProjectId(int $projectId): ?array
    {
        // Find the global rich snippet for the project
        $globalSnippet = $this->dbManager->findByValues([
            'project_id' => $projectId,
            'is_global_snippet' => 1
        ], 'rich_snippets');

        // Return the first result or null if no results
        return !empty($globalSnippet) ? $globalSnippet[0] : null;
    }
}
