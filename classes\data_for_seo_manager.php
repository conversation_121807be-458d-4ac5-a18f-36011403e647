<?php

class DataForSeoManager {
    private $login;
    private $password;
    private $tasks = [];
    private $apiBaseUrl;
    private $dbManager;

    /**
     * @param string $login
     * @param string $password
     * @param string $apiBaseUrl
     */
    public function __construct() {
        global $dbManager;

        $this->login      = ConfigManager::get('data_for_seo.api_login');
        $this->password   = ConfigManager::get('data_for_seo.api_password');
        $this->apiBaseUrl = rtrim(ConfigManager::get('data_for_seo.base_url'), '/') . '/';
        $this->dbManager  = $dbManager;
    }

    public function setApiUrl($apiUrl) {
        $this->apiBaseUrl = rtrim($apiUrl, '/') . '/';
    }

    public function addTask(array $task) {
        $this->tasks[] = $task;
    }

    /**
     * Send tasks to DataForSEO and log each in the DB
     * @param string $endpoint    e.g. "v3/business_data/google/maps/task_post"
     * @param string $apiType     e.g. "business_data/google/maps"
     * @param string $resultType  e.g. "advanced"
     */
    public function sendTasks($endpoint, $resultType = 'advanced') {


        $endpoint = trim($endpoint, '/');
        $segments = explode('/', $endpoint);
        $apiType = implode('/', array_slice($segments, 1, -1));


        $url = $this->apiBaseUrl . $endpoint;
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_USERPWD, "{$this->login}:{$this->password}");
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($this->tasks));

        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            $error = curl_error($ch);
            curl_close($ch);
            throw new Exception("cURL error on sendTasks: {$error}");
        }
        curl_close($ch);

        //print_r($response);

        $data = json_decode($response, true);
        if (isset($data['tasks']) && is_array($data['tasks'])) {
            foreach ($data['tasks'] as $task) {
                $this->dbManager->insertNewRowByData('dataforseo_tasks', [
                    'task_id'     => $task['id'],
                    'tag'         => $task['data']['tag'] ?? null,
                    'api_type'    => $apiType,
                    'result_type' => $resultType,
                    'call_result' => $response,
                    'status'      => 'pending',
                    'created_at'  => date('Y-m-d H:i:s')
                ]);
            }
        }
        $this->tasks = [];
        return $data;
    }

    /**
     * Process all non-completed tasks: fetch results and update each in the DB
     */
    public function processPendingTasks() {
        // find pending tasks
        $pending = $this->dbManager->findByValues(['status' => 'pending'], 'dataforseo_tasks');

        foreach ($pending as $row) {
            $id     =    $row['id'];
            $taskId     = $row['task_id'];
            $apiType    = $row['api_type'];
            $resultType = $row['result_type'];
            $res        = $this->getTaskResultById($taskId, $apiType, $resultType);

            print_r($res);

            if ($res['status'] === 'completed' || $res['status'] === 'failed') {
                $this->dbManager->updateFields($id, 'dataforseo_tasks',['status', 'result_data', 'updated_at'],  [
                    'status'      => $res['status'],
                    'result_data' => json_encode($res['result'] ?? $res['error_details']),
                    'updated_at'  => date('Y-m-d H:i:s')
                ]);
            }
            // pending tasks remain
        }
    }

    public function getTaskResultById($taskId, string $apiType, string $resultType): array {
        $taskId = trim($taskId);
        if (empty($taskId)) {
            return ['status' => 'error', 'error_details' => 'Task ID cannot be empty.'];
        }

        if (!empty($resultType)) {
            $url = sprintf(
                "%sv3/%s/task_get/%s/%s",
                $this->apiBaseUrl,
                trim($apiType, '/'),
                trim($resultType, '/'),
                $taskId
            );
        } else {
            $url = sprintf(
                "%sv3/%s/task_get/%s",
                $this->apiBaseUrl,
                trim($apiType, '/'),
                $taskId
            );
        }


        //print_r($url);
        //exit;

        $response = $this->makeGetRequest($url);

        print_r($response);
        if ($response === null) {
            return ['status' => 'error', 'error_details' => 'API request failed'];
        }

        if (isset($response['status_code'], $response['tasks'][0]['status_code'])) {
            $taskData = $response['tasks'][0];
            switch ($taskData['status_code']) {
                case 20000:
                    return ['status' => 'completed', 'result' => $taskData['result'] ?? null];
                case 40602:
                    return ['status' => 'pending'];
                default:
                    return ['status' => 'failed', 'error_details' => $taskData];
            }
        }
        return ['status' => 'error', 'error_details' => 'Unexpected API response.'];
    }

    private function makeGetRequest(string $url): ?array {

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_USERPWD, "{$this->login}:{$this->password}");
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        //print_r($response);

        if ($httpCode >= 400) {
            error_log("DFS API Error: HTTP {$httpCode} for URL {$url}");
            return null;
        }

        $decoded = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON Decode Error: " . json_last_error_msg());
            return null;
        }

        return $decoded;
    }

    public function clearTasks() {
        $this->tasks = [];
    }

    public function findTaskByTag($tag)
    {
        $result = $this->dbManager->findByColumn('tag', $tag, 'dataforseo_tasks');

        if( ! $result )
            return null;

        if( $result[0] )
            return $result[0];

        return null;
    }

}