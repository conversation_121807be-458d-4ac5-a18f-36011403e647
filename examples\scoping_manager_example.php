<?php

// Include necessary files
require_once '../app/_base/base.php';
require_once '../classes/scoping_manager.php';

// Create a ScopingManager instance
$scopingManager = new ScopingManager($dbManager);

echo "=== Scoping Manager Example ===\n\n";

// Example 1: Add a basic scoping entry
echo "Example 1: Add a basic scoping entry\n";

// Replace with a valid project ID from your database
$projectId = 1;

// Add a scoping entry with minimal data
$result = $scopingManager->addScopingByProjectId($projectId);

if ($result['success']) {
    echo "Success! Scoping entry added with ID: {$result['last_id']}\n";
    echo "Project ID: {$result['scoping_data']['project_id']}\n\n";
} else {
    echo "Error: {$result['error']}\n\n";
}

// Example 2: Add a scoping entry with options and text
echo "Example 2: Add a scoping entry with options and text\n";

// Sample options data
$options = [
    'scope_type' => 'website',
    'priority' => 'high',
    'estimated_hours' => 20
];

// Sample text data
$text = "This project requires a complete website redesign with focus on mobile responsiveness and SEO optimization.";

// Add a scoping entry with options and text
$result = $scopingManager->addScopingByProjectId($projectId, $options, $text);

if ($result['success']) {
    echo "Success! Scoping entry added with ID: {$result['last_id']}\n";
    echo "Project ID: {$result['scoping_data']['project_id']}\n";
    echo "Options: " . print_r(json_decode($result['scoping_data']['options_data'], true), true) . "\n";
    echo "Text: {$result['scoping_data']['text_data']}\n\n";
} else {
    echo "Error: {$result['error']}\n\n";
}
