<?php
/**
 * Overlay zum Anzeigen des Rich Snippet Inhalts
 */

// Daten aus der Datenbank laden
$richSnippetId = $_GET['id'] ?? $_POST['id'] ?? 0;

// Rich Snippet Manager initialisieren
$richSnippetManager = new RichSnippetManager();

// Rich Snippet Daten laden
$richSnippet = $dbManager->findById($richSnippetId, 'rich_snippets');

if (empty($richSnippet)) {
    echo '<div class="alert alert-danger">Rich Snippet nicht gefunden.</div>';
    return;
}

// Rich Snippet Inhalt extrahieren
$richSnippetResult = $richSnippet['rich_snippet_result'] ?? '';

// Formatieren des JSON für bessere Lesbarkeit
$formattedJson = '';
if (!empty($richSnippetResult)) {
    $jsonObj = json_decode($richSnippetResult);
    if ($jsonObj !== null) {
        $formattedJson = json_encode($jsonObj, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);
    } else {
        $formattedJson = $richSnippetResult;
    }
}

// Typ des Rich Snippets bestimmen
$snippetType = 'Seite';
if ($richSnippet['is_global_snippet']) {
    $snippetType = 'Global';
} elseif ($richSnippet['is_homepage']) {
    $snippetType = 'Homepage';
}

// Status des Rich Snippets bestimmen
$status = 'Nicht generiert';
$statusClass = 'danger';
if ($richSnippet['is_published']) {
    $status = 'Veröffentlicht';
    $statusClass = 'success';
} elseif ($richSnippet['is_generated']) {
    $status = 'Generiert';
    $statusClass = 'warning';
}
?>

<div class="modal modal-blur fade show" id="rich-snippet-modal" tabindex="-1" role="dialog" aria-modal="true" style="display: block;">
    <div class="modal-dialog modal-lg modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Rich Snippet Inhalt</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div>
                            <span class="badge text-white bg-blue me-2"><?= htmlspecialchars($snippetType) ?></span>
                            <span class="badge text-white bg-<?= $statusClass ?>"><?= htmlspecialchars($status) ?></span>
                        </div>
                        <div>
                            <a href="<?= htmlspecialchars($richSnippet['url']) ?>" target="_blank" class="btn btn-sm btn-outline-primary">
                                <i class="bi bi-box-arrow-up-right"></i> Seite öffnen
                            </a>
                        </div>
                    </div>
                    
                    <?php if (empty($formattedJson)): ?>
                        <div class="alert alert-warning">
                            <strong>Hinweis:</strong> Für dieses Rich Snippet wurden noch keine Daten generiert.
                        </div>
                    <?php else: ?>
                        <div class="card">
                            <div class="card-header">
                                <h3 class="card-title">LD+JSON Strukturierte Daten</h3>
                            </div>
                            <div class="card-body p-0">
                                <pre class="language-json m-0 p-3" style="max-height: 500px; overflow-y: auto;"><code><?= htmlspecialchars($formattedJson) ?></code></pre>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <div class="modal-footer">
                <?php if ($richSnippet['is_generated'] && !$richSnippet['is_published']): ?>
                    <form method="post" class="d-inline">
                        <input type="hidden" name="class" value="RichSnippetManager">
                        <input type="hidden" name="method" value="publishByRichSnippetId">
                        <input type="hidden" name="richSnippetId" value="<?= $richSnippet['id'] ?>">
                        <input type="hidden" name="action" value="execute">
                        <input type="hidden" name="show_loading" value="1">
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-cloud-upload"></i> Veröffentlichen
                        </button>
                    </form>
                <?php endif; ?>
                
                <?php if (!$richSnippet['is_generated']): ?>
                    <form method="post" class="d-inline">
                        <input type="hidden" name="class" value="RichSnippetManager">
                        <input type="hidden" name="method" value="generateByRichSnippetId">
                        <input type="hidden" name="richSnippetId" value="<?= $richSnippet['id'] ?>">
                        <input type="hidden" name="action" value="execute">
                        <input type="hidden" name="show_loading" value="1">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-gear"></i> Generieren
                        </button>
                    </form>
                <?php endif; ?>
                
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Schließen</button>
            </div>
        </div>
    </div>
</div>
