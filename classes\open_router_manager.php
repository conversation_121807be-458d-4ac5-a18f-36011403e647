<?php

/**
 * OpenRouter Manager Class
 *
 * A class for interacting with the OpenRouter API
 * Provides methods to generate content using various AI models
 */
class OpenRouterManager
{
    private string $apiKey;
    private string $apiUrl = 'https://openrouter.ai/api/v1/chat/completions';
    private string $model = 'anthropic/claude-3-opus:beta';
    private float $temperature = 0.7;
    private int $maxTokens = 10000;
    private string $httpReferer;
    private string $httpSiteUrl;

    /**
     * Constructor
     *
     * @param string $apiKey The OpenRouter API key
     * @param string $httpReferer The HTTP referer for OpenRouter API requests
     * @param string $httpSiteUrl The HTTP site URL for OpenRouter API requests
     */
    public function __construct(string $apiKey, string $httpReferer = '', string $httpSiteUrl = '')
    {
        $this->apiKey = $apiKey;
        $this->httpReferer = $httpReferer ?: 'https://' . ($_SERVER['HTTP_HOST'] ?? 'localhost');
        $this->httpSiteUrl = $httpSiteUrl ?: $this->httpReferer;
    }

    /**
     * Set the model to use
     *
     * @param string $model The model to use
     * @return $this For method chaining
     */
    public function setModel(string $model): self
    {
        $this->model = $model;
        return $this;
    }

    /**
     * Get the current model
     *
     * @return string The current model
     */
    public function getModel(): string
    {
        return $this->model;
    }

    /**
     * Set the temperature
     *
     * @param float $temperature The temperature value (0.0 to 1.0)
     * @return $this For method chaining
     */
    public function setTemperature(float $temperature): self
    {
        $this->temperature = $temperature;
        return $this;
    }

    /**
     * Get the current temperature
     *
     * @return float The current temperature
     */
    public function getTemperature(): float
    {
        return $this->temperature;
    }

    /**
     * Set the maximum number of tokens
     *
     * @param int $maxTokens The maximum number of tokens
     * @return $this For method chaining
     */
    public function setMaxTokens(int $maxTokens): self
    {
        $this->maxTokens = $maxTokens;
        return $this;
    }

    /**
     * Get the current maximum number of tokens
     *
     * @return int The current maximum number of tokens
     */
    public function getMaxTokens(): int
    {
        return $this->maxTokens;
    }

    /**
     * Create a chat completion
     *
     * @param string $prompt The user prompt
     * @param string $systemPrompt The system prompt
     * @return string|null The generated content or null on error
     */
    public function createChatCompletion(string $prompt, string $systemPrompt = ''): ?string
    {
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
            'HTTP-Referer: ' . $this->httpReferer,
            'X-Title: AI Integration',
        ];

        // Prepare messages array
        $messages = [];

        // Add system message if provided
        if (!empty($systemPrompt)) {
            $messages[] = ['role' => 'system', 'content' => $systemPrompt];
        }

        // Add user message
        $messages[] = ['role' => 'user', 'content' => $prompt];

        $data = [
            'model' => $this->model,
            'messages' => $messages,
            'temperature' => $this->temperature,
            'max_tokens' => $this->maxTokens,
        ];

        // Add OpenRouter specific fields
        $data['transforms'] = ['middle-out'];
        $data['route'] = 'fallback';
        $data['site'] = $this->httpSiteUrl;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_TIMEOUT, 1200);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        //print_r($response);

        if ($httpCode < 200 || $httpCode >= 300) {
            error_log("OpenRouter API error: HTTP code $httpCode, Response: $response");
            return null;
        }

        $result = json_decode($response, true);

        print_r($result);



        if (!isset($result['choices'][0]['message']['content'])) {
            error_log("OpenRouter API error: Invalid response format: " . json_encode($result));
            return null;
        }

        $content = $result['choices'][0]['message']['content'];
        $content = trim($content, '```json');
        $content = trim($content, '```');

        return $content;
    }

    /**
     * Create a chat completion with an image
     *
     * @param string $prompt The text prompt
     * @param string $base64Image The base64-encoded image data
     * @param string $systemPrompt Optional system prompt
     * @return string|null The generated content or null on error
     */
    public function createVisionCompletion(string $prompt, string $base64Image, string $systemPrompt = ''): ?string
    {
        $headers = [
            'Content-Type: application/json',
            'Authorization: Bearer ' . $this->apiKey,
            'HTTP-Referer: ' . $this->httpReferer,
            'X-Title: AI Vision Integration',
        ];

        // Prepare messages array
        $messages = [];

        // Add system message if provided
        if (!empty($systemPrompt)) {
            $messages[] = ['role' => 'system', 'content' => $systemPrompt];
        }

        // Add user message with text and image
        $messages[] = [
            'role' => 'user',
            'content' => [
                ['type' => 'text', 'text' => $prompt],
                [
                    'type' => 'image_url',
                    'image_url' => [
                        'url' => 'data:image/jpeg;base64,' . $base64Image
                    ]
                ]
            ]
        ];

        $data = [
            'model' => $this->model, // Use the configured model
            'messages' => $messages,
            'temperature' => $this->temperature,
            'max_tokens' => $this->maxTokens,
        ];

        // Add OpenRouter specific fields
        $data['transforms'] = ['middle-out'];
        $data['route'] = 'fallback';
        $data['site'] = $this->httpSiteUrl;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_TIMEOUT, 120); // Longer timeout for vision requests

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($httpCode < 200 || $httpCode >= 300) {
            error_log("OpenRouter Vision API error: HTTP code $httpCode, Response: $response");
            return null;
        }

        $result = json_decode($response, true);

        if (!isset($result['choices'][0]['message']['content'])) {
            error_log("OpenRouter Vision API error: Invalid response format: " . json_encode($result));
            return null;
        }

        $content = $result['choices'][0]['message']['content'];
        $content = trim($content, '```json');
        $content = trim($content, '```');

        return $content;
    }

    /**
     * Get a list of available models from OpenRouter
     *
     * @return array|null Array of available models or null on error
     */
    public function getAvailableModels(): ?array
    {
        $headers = [
            'Authorization: Bearer ' . $this->apiKey,
            'HTTP-Referer: ' . $this->httpReferer,
        ];

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://openrouter.ai/api/v1/models');
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($httpCode < 200 || $httpCode >= 300) {
            error_log("OpenRouter Models API error: HTTP code $httpCode, Response: $response");
            return null;
        }

        $result = json_decode($response, true);

        if (!isset($result['data'])) {
            error_log("OpenRouter Models API error: Invalid response format: " . json_encode($result));
            return null;
        }

        return $result['data'];
    }
}
