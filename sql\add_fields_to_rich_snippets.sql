-- SQL script to add is_global_snippet and is_homepage fields to rich_snippets table

-- Check if the column is_global_snippet already exists
SET @exists_global = 0;
SELECT COUNT(*) INTO @exists_global FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'rich_snippets' AND column_name = 'is_global_snippet';

-- If the column doesn't exist, add it
SET @query_global = IF(@exists_global = 0, 
    'ALTER TABLE rich_snippets ADD COLUMN is_global_snippet TINYINT(1) DEFAULT 0 AFTER is_published',
    'SELECT "Column is_global_snippet already exists in rich_snippets table."');

PREPARE stmt_global FROM @query_global;
EXECUTE stmt_global;
DEALLOCATE PREPARE stmt_global;

-- Check if the column is_homepage already exists
SET @exists_homepage = 0;
SELECT COUNT(*) INTO @exists_homepage FROM information_schema.columns 
WHERE table_schema = DATABASE() AND table_name = 'rich_snippets' AND column_name = 'is_homepage';

-- If the column doesn't exist, add it
SET @query_homepage = IF(@exists_homepage = 0, 
    'ALTER TABLE rich_snippets ADD COLUMN is_homepage TINYINT(1) DEFAULT 0 AFTER is_global_snippet',
    'SELECT "Column is_homepage already exists in rich_snippets table."');

PREPARE stmt_homepage FROM @query_homepage;
EXECUTE stmt_homepage;
DEALLOCATE PREPARE stmt_homepage;
