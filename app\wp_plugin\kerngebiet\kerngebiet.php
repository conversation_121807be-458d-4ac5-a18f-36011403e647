<?php
/*
Plugin Name: Kerngebiet
Description: Kerngebiet Digital-Plugin.
Version:     1.5.0
Author:      Christian
Text Domain: kerngebiet
*/

defined('ABSPATH') || exit;

// REST-Logic laden
require_once __DIR__ . '/includes/rest-endpoint.php';

// Update-Checker laden
require_once __DIR__ . '/includes/plugin-update-checker-5.5/plugin-update-checker.php';

// Auto-Update konfigurieren

$updateChecker = \YahnisElsts\PluginUpdateChecker\v5\PucFactory::buildUpdateChecker(
    'https://backend.kerngebiet.digital/wp_plugin/kerngebiet/updates/kerngebiet.json',
    __FILE__,
    'kerngebiet'
);

add_filter('wpseo_json_ld_output', '__return_false');

add_filter( 'aioseo_schema_disable', 'my_deactivate_schema' );
function my_deactivate_schema( $disabled ) {
    return true;
}

//LD+JSON ausgeben
add_action('wp_head', 'output_json_ld_snippets', 20);
function output_json_ld_snippets() {
    if (is_admin()) return;

    $home_id = get_option('page_on_front');

    $global = get_post_meta($home_id, 'rich_snippet_global', true);
    if (!empty($global)) {
        echo '<script type="application/ld+json">' . $global . '</script>';
    }

    if (is_singular()) {
        $page_id = get_the_ID();
        $snippet = get_post_meta($page_id, 'rich_snippet', true);
        if (!empty($snippet)) {
            echo '<script type="application/ld+json">' . $snippet . '</script>';
        }
    }
}

add_action('init', function () {
    foreach (['post', 'page'] as $type) {
        register_meta($type, 'rich_snippet', [
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true
        ]);
        register_meta($type, 'rich_snippet_global', [
            'type' => 'string',
            'single' => true,
            'show_in_rest' => true
        ]);
    }
});
