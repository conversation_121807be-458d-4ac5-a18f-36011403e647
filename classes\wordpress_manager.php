<?php

/**
 * WordPress Manager Class
 *
 * A class that interacts with the WordPress REST API to perform various operations
 * such as retrieving media items, posts, and other WordPress content.
 */
class WordpressManager
{
    /**
     * WordPress site URL
     *
     * @var string
     */
    private string $siteUrl;

    /**
     * WordPress API endpoint
     *
     * @var string
     */
    private string $apiEndpoint;

    /**
     * Authentication username
     *
     * @var string|null
     */
    private ?string $username;

    /**
     * Authentication password (Application Password)
     *
     * @var string|null
     */
    private ?string $password;

    /**
     * Whether to verify SSL certificates
     *
     * @var bool
     */
    private bool $verifySSL;

    /**
     * Response headers from the last request
     *
     * @var array
     */
    private array $headers = [];

    /**
     * Constructor
     *
     * @param string $siteUrl The WordPress site URL (without trailing slash)
     * @param string $apiEndpoint The API endpoint (default: wp/v2)
     * @param string|null $username The username for authentication
     * @param string|null $password The application password for authentication
     * @param bool $verifySSL Whether to verify SSL certificates
     */
    public function __construct(
        string $siteUrl,
        string $apiEndpoint = 'wp/v2',
        ?string $username = null,
        ?string $password = null,
        bool $verifySSL = true
    ) {
        $this->siteUrl = rtrim($siteUrl, '/');
        $this->apiEndpoint = $apiEndpoint;
        $this->username = $username;
        $this->password = $password;
        $this->verifySSL = $verifySSL;
    }

    /**
     * Get all images from the WordPress media library
     *
     * @param array $params Additional parameters for the request
     * @return array|null Array of media items or null on error
     */
    public function getAllImages(array $params = []): ?array
    {
        // Default parameters
        $defaultParams = [
            'per_page' => 100,  // Maximum number of items per page
            'media_type' => 'image',  // Only get images
            'orderby' => 'date',  // Order by date
            'order' => 'desc',  // Newest first
        ];

        // Merge default parameters with user-provided parameters
        $params = array_merge($defaultParams, $params);

        // Initialize result array
        $allImages = [];
        $page = 1;
        $morePages = true;

        // Fetch all pages of results
        while ($morePages) {
            $params['page'] = $page;

            // Make the API request
            $response = $this->makeRequest('media', $params);

            // Check if the request was successful
            if ($response === null) {
                return null;
            }

            // If no more items, break the loop
            if (empty($response)) {
                break;
            }

            // Add the current page of results to the full result set
            $allImages = array_merge($allImages, $response);

            // Check if there are more pages
            $totalPages = $this->getLastPageNumber();
            $morePages = ($page < $totalPages);
            $page++;
        }

        return $allImages;
    }

    /**
     * Get images by specific criteria
     *
     * @param array $criteria Criteria to filter images by
     * @return array|null Array of media items or null on error
     */
    public function getImagesByCriteria(array $criteria): ?array
    {
        // Ensure media_type is set to image
        $criteria['media_type'] = 'image';

        return $this->makeRequest('media', $criteria);
    }

    /**
     * Get a single image by ID
     *
     * @param int $id The image ID
     * @return array|null Image data or null on error
     */
    public function getImageById(int $id): ?array
    {
        return $this->makeRequest("media/{$id}");
    }

    /**
     * Get the total number of images
     *
     * @return int|null The total number of images or null on error
     */
    public function getTotalImagesCount(): ?int
    {
        $response = $this->makeRequest('media', [
            'media_type' => 'image',
            'per_page' => 1
        ], true);

        if ($response === null) {
            return null;
        }

        // Get the total from the headers
        return $this->getTotalItems();
    }

    /**
     * Find images without alt text or title
     *
     * This function is useful for SEO and accessibility purposes to identify
     * images that are missing important metadata.
     *
     * @param bool $missingAltOnly If true, only check for missing alt text
     * @param bool $missingTitleOnly If true, only check for missing title
     * @param int $perPage Number of items per page (default: 100)
     * @return array|null Array of images missing alt text or title, or null on error
     */
    public function findImagesWithoutAltOrTitle(bool $missingAltOnly = false, bool $missingTitleOnly = false, int $perPage = 100): ?array
    {
        // Check if we have authentication credentials
        if (empty($this->username) || empty($this->password)) {
            return null;
        }

        // Try to use the Kerngebiet plugin endpoint first
        $pluginImages = $this->findImagesWithoutAltOrTitleViaPlugin();

        // If the plugin endpoint worked, return those results
        if ($pluginImages !== null) {
            return $pluginImages;
        }

        // Fall back to the standard REST API method if the plugin endpoint failed
        return $this->findImagesWithoutAltOrTitleViaRestApi($missingAltOnly, $missingTitleOnly, $perPage);
    }

    /**
     * Find images without alt text or title using the Kerngebiet plugin
     *
     * @return array|null Array of images missing alt text or title with context, or null on error
     */
    public function findImagesWithoutAltOrTitleViaPlugin(): ?array
    {
        // Build the full URL for the Kerngebiet plugin endpoint
        $url = "{$this->siteUrl}/wp-json/kerngebiet/v1/missing-meta-images";

        // Initialize cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60); // Longer timeout as this can be a heavy operation
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);

        // Set SSL verification option
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, $this->verifySSL);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $this->verifySSL ? 2 : 0);

        // Set authentication
        curl_setopt($ch, CURLOPT_USERPWD, "{$this->username}:{$this->password}");

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);


        // Close cURL
        curl_close($ch);

        // Check if the request was successful
        if ($httpCode !== 200 || empty($response)) {
            return null;
        }

        // Parse the response
        $imagesData = json_decode($response, true);

        if (!is_array($imagesData)) {
            return null;
        }

        // Transform the data to match our expected format
        $imagesWithIssues = [];

        foreach ($imagesData as $image) {
            $issues = [];

            // Determine issues based on the data
            if (empty($image['alt_text'])) {
                $issues[] = 'missing_alt_text';
            }

            if (empty($image['attachment_title'])) {
                $issues[] = 'missing_title';
            }

            $imagesWithIssues[] = [
                'id' => $image['attachment_id'],
                'source_url' => $image['image_url'],
                'alt_text' => $image['alt_text'] ?? '',
                'title' => $image['attachment_title'] ?? '',
                'issues' => $issues,
                'page_id' => $image['page_id'] ?? null,
                'page_title' => $image['page_title'] ?? '',
                'page_url' => $image['page_url'] ?? '',
                'context_before' => $image['context_before'] ?? '',
                'context_after' => $image['context_after'] ?? '',
                'meta_description' => $image['meta_description'] ?? '',
                'image_filename' => $image['image_filename'] ?? ''
            ];
        }

        return $imagesWithIssues;
    }

    /**
     * Find images without alt text or title using the standard WordPress REST API
     *
     * This is a fallback method if the plugin endpoint is not available
     *
     * @param bool $missingAltOnly If true, only check for missing alt text
     * @param bool $missingTitleOnly If true, only check for missing title
     * @param int $perPage Number of items per page (default: 100)
     * @return array|null Array of images missing alt text or title, or null on error
     */
    private function findImagesWithoutAltOrTitleViaRestApi(bool $missingAltOnly = false, bool $missingTitleOnly = false, int $perPage = 100): ?array
    {
        // Get all images
        $allImages = $this->getAllImages(['per_page' => $perPage]);

        if ($allImages === null) {
            return null;
        }

        $imagesWithIssues = [];

        foreach ($allImages as $image) {
            $hasIssue = false;
            $issues = [];

            // Check for missing alt text
            if (!$missingTitleOnly && (empty($image['alt_text']) || trim($image['alt_text']) === '')) {
                $hasIssue = true;
                $issues[] = 'missing_alt_text';
            }

            // Check for missing title
            if (!$missingAltOnly && (empty($image['title']['rendered']) || trim($image['title']['rendered']) === '')) {
                $hasIssue = true;
                $issues[] = 'missing_title';
            }

            if ($hasIssue) {
                // Add the image to the result array with issue information
                $imagesWithIssues[] = [
                    'id' => $image['id'],
                    'source_url' => $image['source_url'],
                    'date' => $image['date'],
                    'alt_text' => $image['alt_text'] ?? '',
                    'title' => $image['title']['rendered'] ?? '',
                    'issues' => $issues,
                    'mime_type' => $image['mime_type'],
                    'media_details' => $image['media_details'] ?? []
                ];
            }
        }

        return $imagesWithIssues;
    }

    /**
     * Update image metadata (alt text and/or title)
     *
     * @param int $imageId The ID of the image to update
     * @param array $data The data to update (alt_text and/or title)
     * @return bool True if the update was successful, false otherwise
     */
    public function updateImageMetadata(int $imageId, array $data): bool
    {
        // Check if we have authentication credentials
        if (empty($this->username) || empty($this->password)) {
            return false;
        }

        // Prepare the data for the update
        $updateData = [];

        // Add alt_text if provided
        if (isset($data['alt_text'])) {
            $updateData['alt_text'] = $data['alt_text'];
        }

        // Add title if provided
        if (isset($data['title'])) {
            $updateData['title'] = $data['title'];
        }

        // If no data to update, return false
        if (empty($updateData)) {
            return false;
        }

        // Build the full URL
        $url = "{$this->siteUrl}/wp-json/{$this->apiEndpoint}/media/{$imageId}";

        // Initialize cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($updateData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);

        // Set authentication
        curl_setopt($ch, CURLOPT_USERPWD, "{$this->username}:{$this->password}");

        // Set SSL verification
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, $this->verifySSL);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $this->verifySSL ? 2 : 0);

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        // Close cURL
        curl_close($ch);

        // Check if the request was successful
        return ($httpCode >= 200 && $httpCode < 300);
    }

    /**
     * Download an image from a URL
     *
     * @param string $url The URL of the image to download
     * @return string|null The image data as a string, or null on error
     */
    public function downloadImage(string $url): ?string
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, $this->verifySSL);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $this->verifySSL ? 2 : 0);

        $imageData = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        curl_close($ch);

        if ($httpCode !== 200 || empty($imageData)) {
            return null;
        }

        return $imageData;
    }

    /**
     * Make a request to the WordPress REST API
     *
     * @param string $endpoint The API endpoint
     * @param array $params Query parameters
     * @param bool $headersOnly Whether to only retrieve headers
     * @return array|null Response data or null on error
     */
    private function makeRequest(string $endpoint, array $params = [], $headersOnly = false): ?array
    {
        // Build the full URL
        $url = "{$this->siteUrl}/wp-json/{$this->apiEndpoint}/{$endpoint}";

        // Add query parameters if any
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        // Initialize cURL session
        $ch = curl_init();

        //echo 'headersonly:';
        //echo $headersOnly;
        //exit;

        // Set cURL options
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_HEADER, $headersOnly);

        // Set SSL verification option
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, $this->verifySSL);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $this->verifySSL ? 2 : 0);

        // Set authentication if provided
        if ($this->username && $this->password) {
            curl_setopt($ch, CURLOPT_HTTPAUTH, CURLAUTH_BASIC);
            curl_setopt($ch, CURLOPT_USERPWD, "{$this->username}:{$this->password}");
        }

        // Reset headers array
        $this->headers = [];

        // Set up header capture for all requests
        $headerArray = [];
        curl_setopt($ch, CURLOPT_HEADERFUNCTION,
            function($curl, $header) use (&$headerArray) {
                $parts = explode(':', $header, 2);
                if (count($parts) === 2) {
                    $key = trim($parts[0]);
                    $value = trim($parts[1]);
                    $headerArray[$key] = $value;
                }
                return strlen($header);
            }
        );

        // Execute cURL request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        //echo $response;
        //exit;

        // Store captured headers
        $this->headers = $headerArray;

        if ($headersOnly) {
            // Return empty array for headers-only request
            curl_close($ch);
            return [];
        }

        // Close cURL session
        curl_close($ch);

        // Check for errors
        if ($httpCode >= 400) {
            return null;
        }



        // Parse and return the response
        return json_decode($response, true);
    }

    /**
     * Get the total number of items from the last request
     *
     * @return int|null The total number of items or null if not available
     */
    private function getTotalItems(): ?int
    {
        return isset($this->headers['x-wp-total']) ? (int)$this->headers['x-wp-total'] : null;
    }

    /**
     * Get the last page number from the last request
     *
     * @return int|null The last page number or null if not available
     */
    private function getLastPageNumber(): ?int
    {
        return isset($this->headers['X-WP-TotalPages']) ? (int)$this->headers['X-WP-TotalPages'] : null;
    }

    /**
     * Set the site URL
     *
     * @param string $siteUrl The WordPress site URL
     * @return $this For method chaining
     */
    public function setSiteUrl(string $siteUrl): self
    {
        $this->siteUrl = rtrim($siteUrl, '/');
        return $this;
    }

    /**
     * Set the API endpoint
     *
     * @param string $apiEndpoint The API endpoint
     * @return $this For method chaining
     */
    public function setApiEndpoint(string $apiEndpoint): self
    {
        $this->apiEndpoint = $apiEndpoint;
        return $this;
    }

    /**
     * Set authentication credentials
     *
     * @param string $username The username
     * @param string $password The application password
     * @return $this For method chaining
     */
    public function setAuthentication(string $username, string $password): self
    {
        $this->username = $username;
        $this->password = $password;
        return $this;
    }

    /**
     * Set whether to verify SSL certificates
     *
     * @param bool $verify Whether to verify SSL certificates
     * @return $this For method chaining
     */
    public function setVerifySSL(bool $verify): self
    {
        $this->verifySSL = $verify;
        return $this;
    }

    /**
     * Get all published pages from the WordPress site
     *
     * @param array $params Additional parameters for the request
     * @return array|null Array of page objects or null on error
     */
    public function getAllPublishedPages(array $params = []): ?array
    {
        // Default parameters
        $defaultParams = [
            'per_page' => 100,  // Maximum number of items per page
            'status' => 'publish',  // Only get published pages
            'orderby' => 'date',  // Order by date
            'order' => 'desc',  // Newest first
        ];

        // Merge default parameters with user-provided parameters
        $params = array_merge($defaultParams, $params);

        // Initialize result array
        $allPages = [];
        $page = 1;
        $morePages = true;

        // Fetch all pages of results
        while ($morePages) {
            $params['page'] = $page;

            // Make the API request
            $response = $this->makeRequest('pages', $params);

            // Check if the request was successful
            if ($response === null) {
                return null;
            }

            // If no more items, break the loop
            if (empty($response)) {
                break;
            }

            // Add the current page of results to the full result set
            $allPages = array_merge($allPages, $response);

            // Check if there are more pages
            $totalPages = $this->getLastPageNumber();
            $morePages = ($page < $totalPages);
            $page++;
        }

        return $allPages;
    }

    /**
     * Update page metadata (custom fields)
     *
     * @param int $pageId The ID of the page to update
     * @param array $meta The metadata to update (key-value pairs)
     * @return bool True if the update was successful, false otherwise
     */
    public function updatePageMeta($pageId, array $meta): bool
    {
        // Check if we have authentication credentials
        if (empty($this->username) || empty($this->password)) {
            return false;
        }

        // If no data to update, return false
        if (empty($meta)) {
            return false;
        }

        // Build the full URL
        $url = "{$this->siteUrl}/wp-json/{$this->apiEndpoint}/pages/{$pageId}";

        // Prepare the data for the update
        $updateData = [
            'meta' => $meta
        ];

        //print_r($meta);
        //exit;

        // Initialize cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($updateData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Accept: application/json'
        ]);

        // Set authentication
        curl_setopt($ch, CURLOPT_USERPWD, "{$this->username}:{$this->password}");

        // Set SSL verification
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, $this->verifySSL);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $this->verifySSL ? 2 : 0);

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        //print_r($response);
        //exit;

        // Close cURL
        curl_close($ch);

        // Check if the request was successful
        return ($httpCode >= 200 && $httpCode < 300);
    }

    /**
     * Get page metadata (custom fields)
     *
     * @param int $pageId The ID of the page to get metadata for
     * @return array|null The page metadata or null on error
     */
    public function getPageMeta(int $pageId): ?array
    {
        // Check if we have authentication credentials
        if (empty($this->username) || empty($this->password)) {
            return null;
        }

        // Build the full URL
        $url = "{$this->siteUrl}/wp-json/{$this->apiEndpoint}/pages/{$pageId}";

        // Add query parameters to include meta data
        $url .= '?context=edit';

        // Initialize cURL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Accept: application/json'
        ]);

        // Set authentication
        curl_setopt($ch, CURLOPT_USERPWD, "{$this->username}:{$this->password}");

        // Set SSL verification
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, $this->verifySSL);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, $this->verifySSL ? 2 : 0);

        // Execute the request
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        // Close cURL
        curl_close($ch);

        // Check if the request was successful
        if ($httpCode >= 200 && $httpCode < 300 && !empty($response)) {
            $pageData = json_decode($response, true);

            //print_r($pageData);
            //exit;

            // Extract and return just the meta data
            if (isset($pageData['meta'])) {
                return $pageData['meta'];
            }

            return [];
        }

        return null;
    }

    /**
     * Get WordPress connection settings
     *
     * @return array The WordPress connection settings
     */
    public function getConnectionSettings(): array
    {
        // Prepare settings array
        $settings = [
            'site_url' => $this->siteUrl,
            'api_endpoint' => $this->apiEndpoint,
            'api_url' => "{$this->siteUrl}/wp-json/{$this->apiEndpoint}",
            'has_username' => !empty($this->username),
            'username' => $this->username,
            'has_password' => !empty($this->password),
            'verify_ssl' => $this->verifySSL,
            'is_authenticated' => (!empty($this->username) && !empty($this->password)),
        ];

        // Check if the connection is working
        $settings['is_connected'] = $this->testConnection();

        return $settings;
    }

    /**
     * Get WordPress site settings from the WordPress REST API
     *
     * @return array|null The WordPress site settings or null on error
     */
    public function getSettings(): ?array
    {
        // Check if we have authentication credentials
        if (empty($this->username) || empty($this->password)) {
            return null;
        }

        // Make the API request to the settings endpoint
        return $this->makeRequest('settings');
    }

    /**
     * Test the WordPress connection
     *
     * @return bool True if the connection is working, false otherwise
     */
    private function testConnection(): bool
    {
        // Make a simple request to the WordPress API
        $response = $this->makeRequest('', [], false);

        // If the response is not null, the connection is working
        return $response !== null;
    }
}
