<?php

require_once '../../app/_base/base.php';
require_once '../../classes/scoping_manager.php';

// Initialize response array
$response = ['success' => 0];

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get the website URL
    $websiteUrl = isset($_POST['website_url']) ? trim($_POST['website_url']) : '';
    
    // Get optional parameters
    $options = isset($_POST['options']) && is_array($_POST['options']) ? $_POST['options'] : [];
    $text = isset($_POST['text']) ? trim($_POST['text']) : '';
    
    if (empty($websiteUrl)) {
        $response['error'] = 'Bitte geben Sie eine gültige Website-URL an.';
        $baseManager->outputResponse($response);
        exit;
    }
    
    // Create a ScopingManager instance
    $scopingManager = new ScopingManager();
    
    // Add the scoping entry using the ScopingManager
    $response = $scopingManager->addScopingByWebsiteUrl($websiteUrl, $options, $text);
} else {
    $response['error'] = 'Nur POST-Anfragen werden unterstützt.';
}

// Output the response
$baseManager->outputResponse($response);
