<?php

/**
 * Project Manager Class
 *
 * A class that manages project-related operations, including WordPress website analysis
 * and storing the results in the project database.
 */
class ProjectManager
{
    /**
     * Database manager instance
     *
     * @var DbManager
     */
    private DbManager $dbManager;

    /**
     * Constructor
     *
     * @param DbManager $dbManager The database manager instance
     */
    public function __construct($dbManager = null)
    {
        global $dbManager;
        $this->dbManager = $dbManager;
    }

    /**
     * Add a new project with website URL as required parameter and optional name
     *
     * @param string $websiteUrl The website URL (required)
     * @param string|null $name The project name (optional)
     * @param int|null $clientId The client ID (optional)
     * @return array Response array with success status and project data
     */
    public function addProject(string $websiteUrl, ?int $clientId = null): array
    {
        // Prepare data for the private method
        $projectData = [
            'website_url' => $websiteUrl,
            'client_id' => $clientId
        ];

        // Call the private method to add the project
        return $this->_addProject($projectData);
    }

    public function findProjectById($projectId)
    {
        return $this->dbManager->findById($projectId, 'projects');
    }

    /**
     * Add a competitor project linked to a parent project
     *
     * @param string $websiteUrl The competitor website URL (required)
     * @param int $projectCompetitorParentId The parent project ID (required)
     * @return array Response array with success status and project data
     */
    public function addCompetitorProject(string $websiteUrl, $projectCompetitorParentId): array
    {
        $response = ['success' => 0];

        // Verify that the parent project exists
        $parentProject = $this->dbManager->findById($projectCompetitorParentId, 'projects');
        if (!$parentProject) {
            $response['error'] = 'Das angegebene Elternprojekt existiert nicht.';
            return $response;
        }

        // Prepare data for the private method
        $projectData = [
            'website_url' => $websiteUrl,
            'project_competitor_parent_id' => $projectCompetitorParentId
        ];

        // Call the private method to add the project
        return $this->_addProject($projectData);
    }

    /**
     * Get competitor projects for a specific project
     *
     * @param int $projectId The ID of the parent project
     * @return array Array of competitor projects
     */
    public function getCompetitorProjects(int $projectId): array
    {
        // Verify that the parent project exists
        $parentProject = $this->dbManager->findById($projectId, 'projects');
        if (!$parentProject) {
            return [];
        }

        // Find all projects where project_competitor_parent_id equals the given projectId
        $competitorProjects = $this->dbManager->findByColumn('project_competitor_parent_id', $projectId, 'projects');

        return $competitorProjects ?: [];
    }

    /**
     * Add a new project (private implementation)
     *
     * @param array $data The project data
     * @return array Response array with success status and project data
     */
    private function _addProject(array $data): array
    {
        $response = ['success' => 0];

        // Insert the project
        try {
            $lastId = $this->dbManager->insertNewRowByData('projects', $data);

            if ($lastId) {
                $response['success'] = 1;
                $response['last_id'] = $lastId;
                $response['message'] = 'Projekt erfolgreich angelegt.';
                $response['project_data'] = $data;

                // Generate project summary if requested
                if (!empty($data['website_url']) && !empty($data['analyze_website']) && $data['analyze_website'] === true) {
                    $summaryResponse = $this->generateInfoData($lastId);
                    if ($summaryResponse['success']) {
                        $response['project_summary'] = $summaryResponse['summary'];
                    }
                }
            } else {
                $response['error'] = 'Fehler beim Anlegen des Projekts.';
            }
        } catch (Exception $e) {
            $response['error'] = 'Datenbankfehler: ' . $e->getMessage();
        }

        return $response;
    }

    /**
     * Generate a project summary based on website analysis and store it in the database
     *
     * @param int $projectId The ID of the project
     * @return array Response with success/error information and the generated summary
     */
    public function generateInfoData(int $projectId): array
    {
        $response = ['success' => 0];

        // Get project data
        $project = $this->dbManager->findById($projectId, 'projects');
        if (empty($project)) {
            $response['error'] = "Projekt mit ID {$projectId} nicht gefunden.";
            return $response;
        }

        // Check if website URL is set
        $url = $project['website_url'] ?? '';
        if (empty($url)) {
            $response['error'] = "Für dieses Projekt wurde keine Website-URL angegeben.";
            return $response;
        }

        // Make sure WebsiteCrawler is available
        require_once __DIR__ . '/website_crawler.php';

        // Create AI Manager instance
        $aiManager = new AiManager();

        // Step 1: Analyze homepage
        // Create a WebsiteCrawler for the homepage
        $homepageCrawler = new WebsiteCrawler($url);
        $homepageCrawler->fetch();

        // Get homepage content
        $homepageContent = '';
        if ($homepageCrawler->getStatusCode() == 200) {
            $homepageContent = $homepageCrawler->getFullText();
        } else {
            $response['error'] = "Fehler beim Abrufen der Homepage.";
            return $response;
        }

        // Step 2: Find imprint and extract company info
        // Create Website Manager instance
        $websiteManager = new WebsiteManager($aiManager);

        // Analyze the website to find imprint URL
        $success = $websiteManager->analyzeWebsite($url);

        // Initialize company data
        $companyData = [];

        if ($success) {
            // Get company data
            $companyData = $websiteManager->getCompanyData();

            // Check if project name is empty and company name is available
            if (empty($project['name']) && !empty($companyData['name'])) {
                // Update project name with company name
                $this->dbManager->updateField( $projectId, 'projects', 'name' , $companyData['name'] );

                // Update the project variable to reflect the change
                $project['name'] = $companyData['name'];
            }
        }

        // Step 3: Create a prompt for the AI to generate a summary in German
        $prompt = "Erstelle eine ausführliche Projektzusammenfassung basierend auf den folgenden Website-Informationen:\n";
        $prompt .= "Website URL: {$url}\n";

        // Add ALL company information from $companyData as key-value pairs
        $prompt .= "\nUnternehmensdaten aus dem Impressum:\n";

        if (!empty($companyData)) {
            foreach ($companyData as $key => $value) {
                if (!empty($value) && is_string($value)) {
                    $prompt .= ucfirst($key) . ": " . $value . "\n";
                }
            }
        } else {
            $prompt .= "Keine Unternehmensdaten aus dem Impressum verfügbar.\n";
        }

        // Always include homepage content directly
        if (!empty($homepageContent)) {
            // Limit homepage content to avoid token limits
            $limitedContent = substr($homepageContent, 0, 10000);
            $prompt .= "\nAuszug aus der Homepage:\n{$limitedContent}\n";
        }

        $prompt .= "\nErstelle eine ausführliche Zusammenfassung des Unternehmens inkl. vorhandener Ansprechpartner, Geschäftsführer und Kontaktinformationen.";
        $prompt .= "Beschreibe das Geschäftsmodell, die Produkte oder Dienstleistungen und die Zielgruppe des Unternehmens. ";
        $prompt .= "Halte es professionell und informativ.";

        // Step 4: Generate the summary using AI
        $systemPrompt = "Du bist ein professioneller Projektmanager, der Website-Informationen für ein neues Projekt zusammenfasst. ";
        $systemPrompt .= "Erstelle eine ausführliche und informative Zusammenfassung. ";
        $systemPrompt .= "Nutze alle verfügbaren Informationen, um ein umfassendes Bild des Unternehmens zu zeichnen. ";
        $systemPrompt .= "Antworte auf Deutsch. Keine Formatierungen nur Text";

        try {
            // Generate the summary
            $summary = $aiManager->generateContent($prompt, $systemPrompt);

            // Clean up the summary
            $summary = trim($summary);

            if (empty($summary)) {
                $response['error'] = "Fehler bei der Generierung der Zusammenfassung.";
                return $response;
            }

            // Step 5: Store the summary and company data in the database
            $infoData = $project['info_data'] ?? [];
            $infoData['summary'] = $summary;
            $infoData['generated_at'] = date('Y-m-d H:i:s');

            // Store company data
            $infoData = array_merge($infoData, $companyData);

            // Update the project in the database
            $success = $this->dbManager->updateMergeData($projectId, 'projects', 'info_data', $infoData);

            if (!$success) {
                $response['error'] = "Fehler beim Speichern der Zusammenfassung.";
                return $response;
            }

            // Prepare success response
            $response['success'] = 1;
            $response['message'] = "Zusammenfassung erfolgreich generiert und gespeichert.";

            return $response;
        } catch (Exception $e) {
            error_log("Error generating project summary: " . $e->getMessage());
            $response['error'] = "Fehler bei der Generierung der Zusammenfassung: " . $e->getMessage();
            return $response;
        }
    }

    /**
     * Analyze WordPress images for a project
     *
     * This function uses the WordpressManager to analyze images on a WordPress website
     * and stores the results in the project's wordpress_image_handler_data field.
     *
     * @param int $projectId The ID of the project
     * @return array Response with success/error information
     */
    public function analyzeWordpressImages(int $projectId): array
    {
        $response = ['success' => 0];

        try {
            // Get project data
            $project = $this->dbManager->findById($projectId, 'projects');
            if (empty($project)) {
                $response['error'] = "Projekt mit ID {$projectId} nicht gefunden.";
                return $response;
            }

            // Get WordPress settings from options_data
            $websiteOptions = $project['website_options_data'] ?? [];

            // Check if WordPress credentials are set
            if (empty($websiteOptions['wp_username']) || empty($websiteOptions['wp_password'])) {
                $response['error'] = "WordPress Zugangsdaten sind nicht konfiguriert.";
                return $response;
            }

            // Check for required WordPress settings
            // Use the project's website_url field if wp_site_url is not set
            $wpSiteUrl = $project['website_url'];

            if (empty($wpSiteUrl)) {
                $response['error'] = "Website URL ist nicht konfiguriert.";
                return $response;
            }

            // Create WordPress Manager instance
            $wpManager = new WordpressManager(
                $wpSiteUrl,
                'wp/v2',
                $websiteOptions['wp_username'] ?? null,
                $websiteOptions['wp_password'] ?? null,
                true
            );

            // Get total images count
            $totalImages = $wpManager->getTotalImagesCount();
            if ($totalImages === null) {
                $response['error'] = "Fehler beim Abrufen der Bilder von der WordPress-Website.";
                return $response;
            }

            // Find images without alt text or title
            $imagesWithIssues = $wpManager->findImagesWithoutAltOrTitle();
            if ($imagesWithIssues === null) {
                $response['error'] = "Fehler beim Überprüfen der Bilder auf fehlende Alt-Texte oder Titel.";
                return $response;
            }

            // Count images with missing alt text and title
            $missingAltCount = 0;
            $missingTitleCount = 0;
            $missingBothCount = 0;

            foreach ($imagesWithIssues as $image) {
                $hasAltIssue = in_array('missing_alt_text', $image['issues']);
                $hasTitleIssue = in_array('missing_title', $image['issues']);

                if ($hasAltIssue && $hasTitleIssue) {
                    $missingBothCount++;
                } elseif ($hasAltIssue) {
                    $missingAltCount++;
                } elseif ($hasTitleIssue) {
                    $missingTitleCount++;
                }
            }

            // Prepare website info data
            $websiteInfoData = $project['wordpress_image_handler_data'] ?? [];

            // Update WordPress image analysis data
            $websiteInfoData['wp_total_images'] = $totalImages;
            $websiteInfoData['wp_images_missing_alt'] = $missingAltCount + $missingBothCount;
            $websiteInfoData['wp_images_missing_title'] = $missingTitleCount + $missingBothCount;
            $websiteInfoData['wp_images_missing_both'] = $missingBothCount;
            $websiteInfoData['wp_images_analysis_date'] = date('Y-m-d H:i:s');

            // Store the first 10 problematic images for reference
            $websiteInfoData['wp_problematic_images'] = array_slice($imagesWithIssues, 0, 10);

            // Update the project in the database
            $this->dbManager->updateMergeData($projectId, 'projects', 'wordpress_image_handler_data', $websiteInfoData);

            // Prepare success response
            $response['success'] = 1;
            $response['data'] = [
                'total_images' => $totalImages,
                'images_missing_alt' => $missingAltCount + $missingBothCount,
                'images_missing_title' => $missingTitleCount + $missingBothCount,
                'images_missing_both' => $missingBothCount,
                'analysis_date' => $websiteInfoData['wp_images_analysis_date']
            ];

        } catch (Exception $e) {
            $response['error'] = "Fehler bei der Analyse der WordPress-Bilder: " . $e->getMessage();
        }

        return $response;
    }

    /**
     * Get WordPress image analysis data for a project
     *
     * @param int $projectId The ID of the project
     * @return array|null The WordPress image analysis data or null if not available
     */
    public function getWordpressImageAnalysisData(int $projectId): ?array
    {
        // Get project data
        $project = $this->dbManager->findById($projectId, 'projects');
        if (empty($project) || empty($project['wordpress_image_handler_data'])) {
            return null;
        }

        $websiteInfoData = $project['wordpress_image_handler_data'];

        // Check if WordPress image analysis data exists
        if (!isset($websiteInfoData['wp_total_images'])) {
            return null;
        }

        // Return WordPress image analysis data
        return [
            'total_images' => $websiteInfoData['wp_total_images'],
            'images_missing_alt' => $websiteInfoData['wp_images_missing_alt'],
            'images_missing_title' => $websiteInfoData['wp_images_missing_title'],
            'images_missing_both' => $websiteInfoData['wp_images_missing_both'],
            'analysis_date' => $websiteInfoData['wp_images_analysis_date'] ?? null,
            'problematic_images' => $websiteInfoData['wp_problematic_images'] ?? []
        ];
    }

    /**
     * Check if a project has WordPress enabled
     *
     * @param int $projectId The ID of the project
     * @return bool True if WordPress is enabled, false otherwise
     */
    public function hasWordpressEnabled(int $projectId): bool
    {
        // Get project data
        $project = $this->dbManager->findById($projectId, 'projects');
        if (empty($project)) {
            return false;
        }

        // Check if WordPress credentials are set in options_data
        $websiteOptions = $project['options_data']['website_options_data'] ?? [];
        return !empty($websiteOptions['wp_username']) && !empty($websiteOptions['wp_password']);
    }

    /**
     * Get WordPress settings for a project
     *
     * @param int $projectId The ID of the project
     * @return array|null The WordPress settings or null if not available
     */
    public function getWordpressSettings(int $projectId): ?array
    {
        // Get project data
        $project = $this->dbManager->findById($projectId, 'projects');
        if (empty($project)) {
            return null;
        }

        // Get WordPress settings from options_data
        $websiteOptions = $project['website_options_data'] ?? [];

        // Check if WordPress credentials are set
        if (empty($websiteOptions['wp_username']) || empty($websiteOptions['wp_password'])) {
            return null;
        }

        // Get WordPress site URL (use project's website_url as fallback)
        $wpSiteUrl = $websiteOptions['wp_site_url'] ?? $project['website_url'] ?? null;

        // Return WordPress settings
        return [
            'site_url' => $wpSiteUrl,
            'api_endpoint' => $websiteOptions['wp_api_endpoint'] ?? 'wp/v2',
            'username' => $websiteOptions['wp_username'] ?? null,
            'has_password' => !empty($websiteOptions['wp_password']),
            'verify_ssl' => $websiteOptions['wp_verify_ssl'] ?? true
        ];
    }

}
