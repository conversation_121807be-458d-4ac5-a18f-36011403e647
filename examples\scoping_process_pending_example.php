<?php

// Include necessary files
require_once '../app/_base/base.php';
require_once '../classes/scoping_manager.php';

// Create a ScopingManager instance
$scopingManager = new ScopingManager();

echo "=== Scoping Manager Process Pending Example ===\n\n";

// Get all pending scoping entries
$pendingScopings = $scopingManager->processPending();

if (empty($pendingScopings)) {
    echo "No pending scoping entries found.\n";
} else {
    echo "Found " . count($pendingScopings) . " pending scoping entries:\n\n";
    
    foreach ($pendingScopings as $scoping) {
        echo "ID: {$scoping['id']}, Project ID: {$scoping['project_id']}\n";
        
        // Display options_data if available
        if (!empty($scoping['options_data'])) {
            $options = json_decode($scoping['options_data'], true);
            echo "Options: " . print_r($options, true) . "\n";
        }
        
        // Display text_data if available
        if (!empty($scoping['text_data'])) {
            echo "Text: {$scoping['text_data']}\n";
        }
        
        echo "Created at: {$scoping['created_at']}\n";
        echo "-----------------------------------\n";
    }
    
    // Example: Process the first pending scoping entry
    if (count($pendingScopings) > 0) {
        $firstScoping = $pendingScopings[0];
        echo "\nProcessing first pending scoping entry (ID: {$firstScoping['id']})...\n";
        
        // Here you would implement your processing logic
        // For example, updating the is_completed field:
        $dbManager->updateField($firstScoping['id'], 'scopings', 'is_completed', 1);
        
        echo "Marked scoping entry with ID {$firstScoping['id']} as completed.\n";
    }
}

// Example: Get a limited number of pending entries
echo "\n=== Getting Limited Number of Pending Entries ===\n";
$limitedPendingScopings = $scopingManager->processPending(2);
echo "Found " . count($limitedPendingScopings) . " pending scoping entries with limit=2.\n";
