<?php
/**
 * Example usage of the new WebsiteDiffChecker functions
 * 
 * This example demonstrates how to use:
 * - getLastCrawlByProjectId() (private function, used internally)
 * - isLastCrawlFinished()
 * - getLastCrawledPageHtmlByName()
 */

// Include required files
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../classes/website_diff_checker.php';
require_once __DIR__ . '/../classes/db_manager.php';

// Create database connection
$pdo = new PDO(
    'mysql:host=' . ConfigManager::get('database.host') . ';dbname=' . ConfigManager::get('database.db') . ';charset=utf8mb4',
    ConfigManager::get('database.user'),
    ConfigManager::get('database.pass'),
    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
);

// Make $dbManager global to match the class implementation
global $dbManager;
$dbManager = new DbManager($pdo);

// Create WebsiteDiffChecker instance
$diffChecker = new WebsiteDiffChecker();

// Example project ID (adjust as needed)
$projectId = 1;

echo "=== WebsiteDiffChecker New Functions Example ===\n\n";

// Example 1: Check if the last crawl is finished
echo "Example 1: Check if last crawl is finished\n";
echo "Project ID: {$projectId}\n";

$isFinished = $diffChecker->isLastCrawlFinished($projectId);
echo "Last crawl finished: " . ($isFinished ? "Yes" : "No") . "\n\n";

// Example 2: Get HTML content of a specific page (only if crawl is finished)
if ($isFinished) {
    echo "Example 2: Get HTML content of a specific page\n";
    echo "Project ID: {$projectId}\n";
    
    // Try to get homepage content (adjust page name as needed)
    $pageName = 'homepage.html'; // This should match the actual filename in the zip
    echo "Requesting page: {$pageName}\n";
    
    $htmlContent = $diffChecker->getLastCrawledPageHtmlByName($projectId, $pageName);
    
    if ($htmlContent !== false) {
        echo "Successfully retrieved HTML content!\n";
        echo "Content length: " . strlen($htmlContent) . " characters\n";
        echo "First 200 characters:\n";
        echo substr($htmlContent, 0, 200) . "...\n\n";
    } else {
        echo "Failed to retrieve HTML content for '{$pageName}'\n";
        echo "Possible reasons:\n";
        echo "- Page file doesn't exist in the zip\n";
        echo "- Zip file is corrupted or missing\n";
        echo "- File permissions issue\n\n";
    }
    
    // Example 3: Try to get another page
    echo "Example 3: Try to get another page\n";
    $anotherPageName = 'about.html'; // Adjust as needed
    echo "Requesting page: {$anotherPageName}\n";
    
    $anotherHtmlContent = $diffChecker->getLastCrawledPageHtmlByName($projectId, $anotherPageName);
    
    if ($anotherHtmlContent !== false) {
        echo "Successfully retrieved HTML content for '{$anotherPageName}'!\n";
        echo "Content length: " . strlen($anotherHtmlContent) . " characters\n";
    } else {
        echo "Failed to retrieve HTML content for '{$anotherPageName}'\n";
    }
    
} else {
    echo "Cannot retrieve page content because the last crawl is not finished yet.\n";
    echo "Please wait for the crawl to complete or start a new crawl.\n";
}

echo "\n=== Example completed ===\n";
