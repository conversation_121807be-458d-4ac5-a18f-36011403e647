<?php

require_once '../../app/_base/base.php';
require_once '../../classes/scoping_manager.php';

// Initialize response array
$response = ['success' => 0];

// Check if the request is a POST request
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Get the project ID
    $projectId = isset($_POST['project_id']) ? intval($_POST['project_id']) : 0;
    
    // Get optional parameters
    $options = isset($_POST['options']) && is_array($_POST['options']) ? $_POST['options'] : [];
    $text = isset($_POST['text']) ? trim($_POST['text']) : '';
    
    if (empty($projectId)) {
        $response['error'] = 'Bitte geben Sie eine gültige Projekt-ID an.';
        $baseManager->outputResponse($response);
        exit;
    }
    
    // Create a ScopingManager instance
    $scopingManager = new ScopingManager($dbManager);
    
    // Add the scoping entry using the ScopingManager
    $response = $scopingManager->addScopingByProjectId($projectId, $options, $text);
} else {
    $response['error'] = 'Nur POST-Anfragen werden unterstützt.';
}

// Output the response
$baseManager->outputResponse($response);
