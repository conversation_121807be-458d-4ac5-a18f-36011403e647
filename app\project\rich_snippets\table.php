<?php
// Rich Snippets Table Template
// This template displays a table of rich snippets for a project

// Get project ID
$projectId = $project['id'] ?? 0;

// Initialize RichSnippetManager
$richSnippetManager = new RichSnippetManager();

// Get rich snippets for this project
$richSnippets = $richSnippetManager->getRichSnippetsByProjectId($projectId);
?>

<div class="card mb-4">
    <div class="card-header">
        <h3 class="card-title">Rich Snippets</h3>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-vcenter card-table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Typ</th>
                        <th>URL</th>
                        <th>Status</th>
                        <th class="w-1">Aktionen</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($richSnippets)): ?>
                    <tr>
                        <td colspan="5" class="text-center">Keine Rich Snippets gefunden.</td>
                    </tr>
                    <?php else: ?>
                        <?php foreach ($richSnippets as $snippet): ?>
                        <tr>
                            <td><?= $snippet['id'] ?></td>
                            <td>
                                <?php if ($snippet['is_global_snippet']): ?>
                                    <span class="badge bg-blue text-white">Global</span>
                                <?php elseif ($snippet['is_homepage']): ?>
                                    <span class="badge bg-green text-white">Homepage</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary text-white">Seite</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?= htmlspecialchars($snippet['url']) ?>" target="_blank">
                                    <?= htmlspecialchars(substr($snippet['url'], 0, 50) . (strlen($snippet['url']) > 50 ? '...' : '')) ?>
                                </a>
                            </td>
                            <td>
                                <?php if ($snippet['is_published']): ?>
                                    <span class="badge bg-success text-white">Veröffentlicht</span>
                                <?php elseif ($snippet['is_generated']): ?>
                                    <span class="badge bg-warning text-white">Generiert</span>
                                <?php else: ?>
                                    <span class="badge bg-danger text-white">Nicht generiert</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group">
                                    <form action="" method="post" class="d-inline">
                                        <input type="hidden" name="table" value="rich_snippets">
                                        <input type="hidden" name="id" value="<?= $snippet['id'] ?>">
                                        <input type="hidden" name="overlay_template" value="project/rich_snippets/rich_snippet_overlay">
                                        <input type="hidden" name="action" value="overlay">
                                        <button type="submit" class="btn btn-sm btn-outline-primary me-1">
                                            Ansehen
                                        </button>
                                    </form>

                                    <form action="" method="post" class="d-inline">
                                        <input type="hidden" name="class" value="RichSnippetManager">
                                        <input type="hidden" name="method" value="generateByRichSnippetId">
                                        <input type="hidden" name="richSnippetId" value="<?= $snippet['id'] ?>">
                                        <input type="hidden" name="action" value="execute">
                                        <input type="hidden" name="show_loading" value="1">
                                        <button type="submit" class="btn btn-sm btn-outline-primary me-1">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-world-download">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M21 12a9 9 0 1 0 -9 9" />
                                                <path d="M3.6 9h16.8" />
                                                <path d="M3.6 15h8.4" />
                                                <path d="M11.578 3a17 17 0 0 0 0 18" />
                                                <path d="M12.5 3c1.719 2.755 2.5 5.876 2.5 9" />
                                                <path d="M18 14v7m-3 -3l3 3l3 -3" />
                                            </svg>
                                            Generieren
                                        </button>
                                    </form>

                                    <?php if (!$snippet['is_global_snippet']): ?>
                                    <form action="" method="post" class="d-inline">
                                        <input type="hidden" name="class" value="RichSnippetManager">
                                        <input type="hidden" name="method" value="publishByRichSnippetId">
                                        <input type="hidden" name="richSnippetId" value="<?= $snippet['id'] ?>">
                                        <input type="hidden" name="action" value="execute">
                                        <input type="hidden" name="show_loading" value="1">
                                        <button type="submit" class="btn btn-sm btn-outline-primary">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon icon-tabler icons-tabler-outline icon-tabler-world-download">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M21 12a9 9 0 1 0 -9 9" />
                                                <path d="M3.6 9h16.8" />
                                                <path d="M3.6 15h8.4" />
                                                <path d="M11.578 3a17 17 0 0 0 0 18" />
                                                <path d="M12.5 3c1.719 2.755 2.5 5.876 2.5 9" />
                                                <path d="M18 14v7m-3 -3l3 3l3 -3" />
                                            </svg>
                                            Veröffentlichen
                                        </button>
                                    </form>
                                    <?php endif; ?>

                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
